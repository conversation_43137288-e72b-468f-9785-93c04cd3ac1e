<div class="inventory-management">
  <!-- Modern Header -->
  <mat-toolbar color="primary" class="app-toolbar">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <div class="toolbar-logo clickable-logo" (click)="navigateToDashboard()">
      <img
        src="assets/images/Absa_logo.png"
        alt="Absa Bank Logo"
        class="toolbar-absa-logo"
        loading="lazy"
        onerror="this.style.display='none'">
    </div>

    <span class="toolbar-title">Cash Management System - Inventory</span>

    <span class="toolbar-spacer"></span>

    <!-- Quick Actions -->
    <div class="toolbar-actions" *ngIf="userService.hasManagerPrivileges()">
      <button mat-icon-button [matMenuTriggerFor]="actionsMenu" class="more-actions">
        <mat-icon>more_vert</mat-icon>
      </button>
    </div>

    <mat-menu #actionsMenu="matMenu">
      <button mat-menu-item>
        <mat-icon>file_download</mat-icon>
        <span>Export Report</span>
      </button>
    </mat-menu>

    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item>
        <mat-icon>person</mat-icon>
        <span>{{ currentUser?.fullName }}</span>
      </button>
      <button mat-menu-item>
        <mat-icon>business</mat-icon>
        <span>{{ currentUser?.department }}</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <div class="content-container">
    <!-- Modern Hero Section -->
    <div class="hero-section" *ngIf="inventorySummary">
      <div class="hero-header">
        <h1 class="hero-title">Inventory Overview</h1>
        <p class="hero-subtitle">Real-time cash inventory management and monitoring</p>
      </div>

      <!-- Enhanced Summary Cards -->
      <div class="summary-grid">
        <!-- Total Value Card -->
        <div class="modern-summary-card value-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="card-trend positive">
                <mat-icon>trending_up</mat-icon>
                <span>+2.5%</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ formatCurrency(inventorySummary.totalValue) }}</div>
              <div class="card-label">Total Inventory Value</div>
              <div class="card-description">Across all note series</div>
            </div>
          </div>
        </div>

        <!-- Total Notes Card -->
        <div class="modern-summary-card notes-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>receipt_long</mat-icon>
              </div>
              <div class="card-trend neutral">
                <mat-icon>remove</mat-icon>
                <span>0%</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ formatNumber(inventorySummary.totalNotes) }}</div>
              <div class="card-label">Total Notes</div>
              <div class="card-description">Physical bank notes</div>
            </div>
          </div>
        </div>

        <!-- Low Stock Alerts Card -->
        <div class="modern-summary-card alerts-card" [class.critical]="inventorySummary.lowStockAlerts.length > 5">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>warning</mat-icon>
                <div class="alert-badge" *ngIf="inventorySummary.lowStockAlerts.length > 0">!</div>
              </div>
              <div class="card-trend" [class.warning]="inventorySummary.lowStockAlerts.length > 0">
                <mat-icon>priority_high</mat-icon>
                <span>{{ inventorySummary.lowStockAlerts.length > 0 ? 'Alert' : 'OK' }}</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ inventorySummary.lowStockAlerts.length }}</div>
              <div class="card-label">Low Stock Alerts</div>
              <div class="card-description">Require attention</div>
            </div>
          </div>
        </div>

        <!-- Series Count Card -->
        <div class="modern-summary-card series-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>category</mat-icon>
              </div>
              <div class="card-trend positive">
                <mat-icon>check_circle</mat-icon>
                <span>Active</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ getSeriesArray().length }}</div>
              <div class="card-label">Note Series</div>
              <div class="card-description">Available types</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Inventory Section -->
    <div class="inventory-section">
      <div class="inventory-container">
        <div class="section-header">
          <h2 class="section-title">Detailed Inventory</h2>
          <p class="section-subtitle">Manage cash inventory by note series</p>
        </div>

        <div class="inventory-content">
          <mat-tab-group class="modern-tabs" color="primary">
            <mat-tab *ngFor="let series of getSeriesArray()" [label]="NOTE_SERIES_LABELS[series]">
              <div class="tab-content">
                <!-- Series Overview Cards -->
                <div class="series-overview">
                  <div class="series-stats-grid">
                    <div class="series-stat-card">
                      <div class="stat-icon">
                        <mat-icon>receipt_long</mat-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-value">{{ formatSeriesQuantityDisplay(series) }}</div>
                        <div class="stat-label">Total Notes</div>
                      </div>
                    </div>
                    <div class="series-stat-card">
                      <div class="stat-icon">
                        <mat-icon>account_balance_wallet</mat-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-value">{{ formatCurrency(getSeriesTotal(series).value) }}</div>
                        <div class="stat-label">Total Value</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Modern Inventory Grid -->
                <div class="inventory-grid">
                  <div class="inventory-item"
                       *ngFor="let item of inventoryBreakdown[series]"
                       [class]="getSeriesStyleClass(series)">
                    <div class="item-header">
                      <div class="denomination-info">
                        <div class="denomination-icon">
                          <img [src]="getDenominationImage(item.denomination, series)"
                               [alt]="getDenominationLabel(item.denomination) + ' - ' + NOTE_SERIES_LABELS[series]"
                               class="note-image"
                               loading="lazy"
                               (error)="onImageError($event)">
                          <mat-icon *ngIf="!getDenominationImage(item.denomination, series)" class="fallback-icon">payments</mat-icon>
                        </div>
                        <div class="denomination-details">
                          <h3 class="denomination-title">{{ getDenominationLabel(item.denomination) }}</h3>
                          <p class="denomination-series">{{ NOTE_SERIES_LABELS[series] }}</p>
                          <div class="denomination-value">{{ formatCurrency(item.denomination) }}</div>
                        </div>
                      </div>
                      <div class="item-status">
                        <mat-chip [class]="getStockStatus(item).class">
                          <mat-icon>{{ getStockStatusIcon(item) }}</mat-icon>
                          {{ getStockStatus(item).status }}
                        </mat-chip>
                      </div>
                    </div>

                    <div class="item-body">
                      <div class="item-metrics">
                        <div class="metric">
                          <div class="metric-value">{{ formatQuantityDisplay(item.quantity) }}</div>
                          <div class="metric-label">Quantity</div>
                        </div>
                        <div class="metric">
                          <div class="metric-value">{{ formatCurrency(item.value) }}</div>
                          <div class="metric-label">Total Value</div>
                        </div>
                      </div>

                      <div class="item-actions" *ngIf="userService.hasManagerPrivileges()">
                        <button mat-raised-button
                                color="primary"
                                (click)="onAddCash(item.noteSeries, item.denomination)"
                                class="add-btn">
                          <mat-icon>add</mat-icon>
                          Add Cash
                        </button>
                      </div>
                    </div>

                    <!-- Progress Bar for Stock Level -->
                    <div class="stock-progress">
                      <mat-progress-bar
                        [value]="getStockPercentage(item)"
                        [color]="getStockProgressColor(item)"
                        mode="determinate">
                      </mat-progress-bar>
                      <div class="progress-label">
                        <span>Stock Level: {{ getStockPercentage(item) }}%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- No Data State -->
                <div class="no-data" *ngIf="inventoryBreakdown[series].length === 0">
                  <div class="no-data-icon">
                    <mat-icon>inventory_2</mat-icon>
                  </div>
                  <h3>No Inventory Data</h3>
                  <p>No inventory data available for {{ NOTE_SERIES_LABELS[series] }}</p>
                  <button mat-raised-button color="primary" (click)="onAddCash(series)" *ngIf="userService.hasManagerPrivileges()">
                    <mat-icon>add_circle</mat-icon>
                    Add Initial Stock
                  </button>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
</div>
