import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Angular Material Imports
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';
import { MatProgressBarModule } from '@angular/material/progress-bar';

// Models
import { User } from '../../models/user.model';
import { CashInventory, NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS, InventorySummary, LowStockAlert, AlertSeverity } from '../../models/inventory.model';

// Services
import { UserService } from '../../services/user.service';
import { InventoryService } from '../../services/inventory.service';

// Components
import { AddCashModalComponent } from '../add-cash-modal/add-cash-modal.component';

@Component({
  selector: 'app-inventory-management',
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatTabsModule,
    MatSnackBarModule,
    MatDialogModule,
    MatMenuModule,
    MatDividerModule,
    MatBadgeModule,
    MatProgressBarModule
  ],
  templateUrl: './inventory-management.component.html',
  styleUrls: ['./inventory-management.component.scss']
})
export class InventoryManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentUser: User | null = null;
  inventoryBreakdown: { [key in NoteSeries]: CashInventory[] } = {
    [NoteSeries.MANDELA]: [],
    [NoteSeries.BIG_5]: [],
    [NoteSeries.COMMEMORATIVE]: [],
    [NoteSeries.V6]: []
  };
  inventorySummary: InventorySummary | null = null;

  // Constants for templates
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;
  DENOMINATION_LABELS = DENOMINATION_LABELS;
  NoteSeries = NoteSeries;

  // Table columns
  displayedColumns: string[] = ['denomination', 'quantity', 'value', 'status', 'actions'];

  constructor(
    public userService: UserService,
    private inventoryService: InventoryService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.currentUser = this.userService.getCurrentUser();

    if (!this.currentUser || !this.hasInventoryAccess()) {
      this.router.navigate(['/login']);
      return;
    }

    this.setupSubscriptions();
    this.loadInventoryData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private hasInventoryAccess(): boolean {
    return this.userService.hasManagerPrivileges() || this.currentUser?.role === 'issuer';
  }

  private setupSubscriptions(): void {
    this.inventoryService.inventory$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadInventoryData();
      });
  }

  private loadInventoryData(): void {
    this.inventoryBreakdown = this.inventoryService.getDetailedInventoryBreakdown();
    this.inventorySummary = this.inventoryService.getInventorySummary();
  }

  getSeriesArray(): NoteSeries[] {
    return Object.values(NoteSeries);
  }

  getSeriesTotal(series: NoteSeries): { quantity: number; value: number } {
    const items = this.inventoryBreakdown[series];
    return {
      quantity: items.reduce((sum, item) => sum + item.quantity, 0),
      value: items.reduce((sum, item) => sum + item.value, 0)
    };
  }

  getStockStatus(item: CashInventory): { status: string; class: string } {
    if (!this.inventorySummary) return { status: 'Unknown', class: 'status-unknown' };

    const alert = this.inventorySummary.lowStockAlerts.find(a => a.inventoryId === item.id);

    if (!alert) {
      return { status: 'In Stock', class: 'status-in-stock' };
    }

    switch (alert.severity) {
      case AlertSeverity.CRITICAL:
        return { status: 'Critical', class: 'status-critical' };
      case AlertSeverity.HIGH:
        return { status: 'Low Stock', class: 'status-low' };
      case AlertSeverity.MEDIUM:
        return { status: 'Medium', class: 'status-medium' };
      case AlertSeverity.LOW:
        return { status: 'Watch', class: 'status-watch' };
      default:
        return { status: 'In Stock', class: 'status-in-stock' };
    }
  }

  onAddCash(series?: NoteSeries, denomination?: NoteDenomination): void {
    if (!this.userService.hasManagerPrivileges()) {
      this.snackBar.open('Insufficient privileges to add cash', 'Close', { duration: 3000 });
      return;
    }

    const dialogRef = this.dialog.open(AddCashModalComponent, {
      width: '900px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      disableClose: true,
      panelClass: 'custom-dialog-container',
      data: { series, denomination }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success) {
        this.loadInventoryData();
        this.snackBar.open(`Successfully added ${result.added} notes to inventory`, 'Close', { duration: 3000 });
      }
    });
  }



  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: NoteDenomination): string {
    return DENOMINATION_LABELS[denomination];
  }

  goBack(): void {
    if (this.userService.hasManagerPrivileges()) {
      this.router.navigate(['/manager-dashboard']);
    } else if (this.currentUser?.role === 'issuer') {
      this.router.navigate(['/issuer-dashboard']);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  navigateToDashboard(): void {
    if (this.userService.hasManagerPrivileges()) {
      this.router.navigate(['/manager-dashboard']);
    } else if (this.currentUser?.role === 'issuer') {
      this.router.navigate(['/issuer-dashboard']);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  // Convert quantity to batches and singles display
  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }

  // Get total batches for a series
  getSeriesBatches(series: NoteSeries): number {
    const total = this.getSeriesTotal(series);
    return Math.floor(total.quantity / 100);
  }

  // Get total singles for a series
  getSeriesSingles(series: NoteSeries): number {
    const total = this.getSeriesTotal(series);
    return total.quantity % 100;
  }

  // Format series total display
  formatSeriesQuantityDisplay(series: NoteSeries): string {
    const total = this.getSeriesTotal(series);
    return this.formatQuantityDisplay(total.quantity);
  }

  logout(): void {
    this.userService.logout();
    this.router.navigate(['/login']);
  }

  // New methods for enhanced UI
  getDenominationImage(denomination: NoteDenomination, series?: NoteSeries): string {
    // Return path to denomination images based on series
    if (!series) {
      // Default to Money folder for general use
      const imageMap: { [key in NoteDenomination]: string } = {
        [NoteDenomination.R10]: 'assets/images/Money/R10.jpg',
        [NoteDenomination.R20]: 'assets/images/Money/R20.jpg',
        [NoteDenomination.R50]: 'assets/images/Money/R50.jpg',
        [NoteDenomination.R100]: 'assets/images/Money/R100.jpg',
        [NoteDenomination.R200]: 'assets/images/Money/R200.jpg'
      };
      return imageMap[denomination] || '';
    }

    // Series-specific images
    const seriesFolderMap: { [key in NoteSeries]: string } = {
      [NoteSeries.MANDELA]: 'Mandela',
      [NoteSeries.BIG_5]: 'Big 5',
      [NoteSeries.COMMEMORATIVE]: 'Commemorative Series',
      [NoteSeries.V6]: 'V6'
    };

    const denominationFileMap: { [key in NoteDenomination]: string } = {
      [NoteDenomination.R10]: 'R10.jpg',
      [NoteDenomination.R20]: 'R20.jpg',
      [NoteDenomination.R50]: 'R50.jpg',
      [NoteDenomination.R100]: 'R100.jpg',
      [NoteDenomination.R200]: 'R200.jpg'
    };

    const seriesFolder = seriesFolderMap[series];
    const denominationFile = denominationFileMap[denomination];

    return `assets/images/${seriesFolder}/${denominationFile}`;
  }

  // Get coin image
  getCoinImage(): string {
    return 'assets/images/Money/Coin.png';
  }

  // Get dye stained image
  getDyeStainedImage(): string {
    return 'assets/images/Money/Dye Stained.jpg';
  }

  getStockStatusIcon(item: CashInventory): string {
    const status = this.getStockStatus(item);
    const iconMap: { [key: string]: string } = {
      'status-in-stock': 'check_circle',
      'status-watch': 'visibility',
      'status-medium': 'warning',
      'status-low': 'error_outline',
      'status-critical': 'dangerous',
      'status-unknown': 'help_outline'
    };
    return iconMap[status.class] || 'help_outline';
  }

  getStockPercentage(item: CashInventory): number {
    // Calculate stock percentage based on some threshold
    // For demo purposes, using a simple calculation
    const maxStock = 1000; // Assume max stock of 1000 notes per denomination
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: CashInventory): 'primary' | 'accent' | 'warn' {
    const percentage = this.getStockPercentage(item);
    if (percentage < 20) return 'warn';
    if (percentage < 50) return 'accent';
    return 'primary';
  }

  // Get highest denomination for preview images
  getHighestDenomination(): NoteDenomination {
    return NoteDenomination.R200;
  }

  // Get series-specific styling class
  getSeriesStyleClass(series: NoteSeries): string {
    const classMap: { [key in NoteSeries]: string } = {
      [NoteSeries.MANDELA]: 'mandela-series',
      [NoteSeries.BIG_5]: 'big5-series',
      [NoteSeries.COMMEMORATIVE]: 'commemorative-series',
      [NoteSeries.V6]: 'v6-series'
    };
    return classMap[series];
  }

  // Handle image loading errors
  onImageError(event: any): void {
    const img = event.target;
    img.style.display = 'none';
    // Show fallback icon
    const fallbackIcon = img.parentElement?.querySelector('.fallback-icon');
    if (fallbackIcon) {
      fallbackIcon.style.display = 'block';
    }
  }
}
