.report-issue-modal {
  min-width: 600px;
  max-width: 800px;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--absa-red);
  margin-bottom: 1rem;
}

.issue-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.full-width {
  width: 100%;
}

.request-info {
  background: var(--absa-light-gray);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--absa-dark-blue);
  margin-bottom: 0.5rem;
}

.request-details {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
}

.request-id {
  font-family: monospace;
  background: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.category-option,
.priority-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priority-critical { color: var(--absa-red); }
.priority-high { color: #ff6b35; }
.priority-medium { color: #f7931e; }
.priority-low { color: #4caf50; }

.priority-guidelines {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.priority-guidelines h4 {
  margin: 0 0 0.5rem 0;
  color: var(--absa-dark-blue);
}

.guideline-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.guideline-item.critical mat-icon { color: var(--absa-red); }
.guideline-item.high mat-icon { color: #ff6b35; }
.guideline-item.medium mat-icon { color: #f7931e; }
.guideline-item.low mat-icon { color: #4caf50; }

mat-dialog-actions {
  padding: 1rem 0 0 0;
}
