.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-toolbar {
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1); // Make logo white on red background
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    margin-left: 16px;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .logout-button {
    margin-left: 8px;
    color: white;

    mat-icon {
      margin-right: 4px;
    }
  }
}

// Enhanced accessibility
@media (prefers-reduced-motion: reduce) {
  .toolbar-absa-logo {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }
  }
}

.dashboard-content {
  padding: 1.5rem 2rem;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;

  // Large screens - utilize more space
  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 1.5rem 3rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1600px;
    padding: 1.5rem 2.5rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    max-width: 1200px;
    padding: 1.5rem 2rem;
  }

  // Small-medium screens - reduced padding
  @media (min-width: 600px) and (max-width: 899px) {
    padding: 1.5rem;
  }

  @media (max-width: 599px) {
    padding: 1rem;
  }
}

.welcome-section {
  margin-bottom: 2rem;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 4 cards in a row
  @media (min-width: 1200px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 600px) and (max-width: 899px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  @media (max-width: 599px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  @media (max-width: 480px) {
    gap: 0.75rem;
    grid-template-columns: 1fr;
  }

  @media (max-width: 360px) {
    gap: 0.5rem;
    grid-template-columns: 1fr;
  }
}

.stat-card {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }

  &:hover {
    transform: translateY(-2px);
  }

  &.pending {
    border-left: 4px solid #ff9800;
  }

  &.active {
    border-left: 4px solid #2196f3;
  }

  &.completed {
    border-left: 4px solid #4caf50;
  }

  &.total {
    border-left: 4px solid #9c27b0;
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: 1rem;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: #666;
    }

    .stat-info {
      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.9rem;
        color: #666;
        margin-top: 0.25rem;
      }
    }
  }
}

.welcome-card {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 12px;

  mat-card-header {
    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
    }

    mat-card-subtitle {
      font-size: 1rem;
      color: #666;
      margin-top: 0.5rem;
    }
  }

  mat-card-content {
    p {
      font-size: 1rem;
      color: #555;
      margin: 1rem 0;
    }
  }

  mat-card-actions {
    padding: 1rem 1.5rem;

    button {
      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

.requests-section {
  .requests-card {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-radius: 12px;

    mat-card-header {
      mat-card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
      }

      mat-card-subtitle {
        color: #666;
      }
    }
  }
}

.table-container {
  overflow-x: auto;
  margin-top: 1rem;
}

.requests-table {
  width: 100%;

  th {
    font-weight: 600;
    color: #333;
  }

  td {
    padding: 1rem 0.5rem;
  }

  .status-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    color: white !important;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: white !important;
    }

    span {
      line-height: 1;
      color: white !important;
    }

    &.status-pending {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border: 1px solid #ff9800;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-approved {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      border: 1px solid #2196f3;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-issued {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
      border: 1px solid #9c27b0;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-returned {
      background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
      border: 1px solid #00bcd4;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-completed {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      border: 1px solid #4caf50;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-cancelled {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      border: 1px solid #f44336;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-rejected {
      background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
      border: 1px solid #e91e63;

      mat-icon, span {
        color: white !important;
      }
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
  }

  mat-chip {
    mat-icon {
      margin-right: 0.25rem;
      font-size: 1rem;
    }
  }
}

.no-requests {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    color: #ccc;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }
}



.tab-content {
  padding: 1.5rem 0;
}

// Tab customizations
::ng-deep .mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-body-content {
    padding: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .requests-table {
    font-size: 0.8rem;

    th, td {
      padding: 0.5rem 0.25rem;
    }
  }

  .welcome-card {
    mat-card-header {
      mat-card-title {
        font-size: 1.25rem;
      }

      mat-card-subtitle {
        font-size: 0.9rem;
      }
    }
  }

  .stat-card .stat-content {
    flex-direction: column;
    text-align: center;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .stat-info .stat-number {
      font-size: 1.5rem;
    }
  }
}
