<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Management System - Domain Models Visualizer</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-coins"></i>
                    <h1>Cash Management System</h1>
                    <span class="subtitle">Domain Models Visualizer</span>
                </div>
                <div class="header-controls">
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-refresh"></i> Reset Layout
                    </button>
                    <button class="btn btn-primary" id="exportBtn">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-content">
                <h3><i class="fas fa-filter"></i> Filters</h3>
                <div class="filter-group">
                    <label class="filter-label">
                        <input type="checkbox" id="showAll" checked>
                        <span class="checkmark"></span>
                        Show All Entities
                    </label>
                </div>
                
                <div class="domain-filters">
                    <h4>Domain Categories</h4>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="user" checked>
                        <span class="checkmark user"></span>
                        User Management
                    </label>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="request" checked>
                        <span class="checkmark request"></span>
                        Cash Requests
                    </label>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="inventory" checked>
                        <span class="checkmark inventory"></span>
                        Inventory
                    </label>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="transaction" checked>
                        <span class="checkmark transaction"></span>
                        Transactions
                    </label>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="notification" checked>
                        <span class="checkmark notification"></span>
                        Notifications
                    </label>
                    <label class="filter-label">
                        <input type="checkbox" class="domain-filter" data-domain="issue" checked>
                        <span class="checkmark issue"></span>
                        Issues
                    </label>
                </div>

                <div class="legend">
                    <h4>Legend</h4>
                    <div class="legend-item">
                        <div class="legend-color user"></div>
                        <span>User Management</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color request"></div>
                        <span>Cash Requests</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color inventory"></div>
                        <span>Inventory</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color transaction"></div>
                        <span>Transactions</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color notification"></div>
                        <span>Notifications</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color issue"></div>
                        <span>Issues</span>
                    </div>
                </div>

                <div class="stats">
                    <h4>Statistics</h4>
                    <div class="stat-item">
                        <span class="stat-label">Total Entities:</span>
                        <span class="stat-value" id="totalEntities">12</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Relationships:</span>
                        <span class="stat-value" id="totalRelationships">24</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Visible:</span>
                        <span class="stat-value" id="visibleEntities">12</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Canvas -->
        <main class="main-content">
            <div class="canvas-container">
                <svg id="relationshipCanvas" class="relationship-canvas">
                    <!-- Relationship lines will be drawn here -->
                </svg>
                <div id="entitiesContainer" class="entities-container">
                    <!-- Entity boxes will be dynamically created here -->
                </div>
            </div>
        </main>

        <!-- Entity Detail Modal -->
        <div id="entityModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">Entity Details</h2>
                    <button class="modal-close" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="entity-info">
                        <div class="info-section">
                            <h3>Properties</h3>
                            <div id="entityProperties" class="properties-list">
                                <!-- Properties will be populated here -->
                            </div>
                        </div>
                        <div class="info-section">
                            <h3>Relationships</h3>
                            <div id="entityRelationships" class="relationships-list">
                                <!-- Relationships will be populated here -->
                            </div>
                        </div>
                        <div class="info-section">
                            <h3>Business Rules</h3>
                            <div id="entityRules" class="rules-list">
                                <!-- Business rules will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="editEntityBtn">
                        <i class="fas fa-edit"></i> Edit Entity
                    </button>
                    <button class="btn btn-primary" id="saveEntityBtn" style="display: none;">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading Domain Models...</p>
            </div>
        </div>

        <!-- Notification Toast -->
        <div id="toast" class="toast">
            <div class="toast-content">
                <i class="fas fa-check-circle"></i>
                <span id="toastMessage">Action completed successfully!</span>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
