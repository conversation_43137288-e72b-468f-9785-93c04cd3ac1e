export interface CashInventory {
  id: string;
  noteSeries: NoteSeries;
  denomination: NoteDenomination;
  quantity: number;
  value: number;
  lastUpdated: Date;
  updatedBy: string;
}

export interface CoinInventory {
  id: string;
  denomination: CoinDenomination;
  quantity: number;
  batches: number;
  value: number;
  lastUpdated: Date;
  updatedBy: string;
}

export enum NoteSeries {
  MANDELA = 'mandela',
  BIG_5 = 'big_5',
  COMMEMORATIVE = 'commemorative',
  V6 = 'v6'
}

export enum NoteDenomination {
  R10 = 10,
  R20 = 20,
  R50 = 50,
  R100 = 100,
  R200 = 200
}

export enum CoinDenomination {
  R5 = 5,
  R2 = 2,
  R1 = 1,
  C50 = 0.5,
  C20 = 0.2,
  C10 = 0.1
}

export interface InventoryTransaction {
  id: string;
  type: TransactionType;
  inventoryId: string;
  quantityChange: number;
  previousQuantity: number;
  newQuantity: number;
  reason: string;
  performedBy: string;
  timestamp: Date;
}

export enum TransactionType {
  ADD = 'add',
  REMOVE = 'remove',
  ADJUST = 'adjust',
  ISSUE = 'issue',
  RETURN = 'return'
}

export interface InventorySummary {
  totalValue: number;
  totalNotes: number;
  seriesBreakdown: SeriesBreakdown[];
  denominationBreakdown: DenominationBreakdown[];
  lowStockAlerts: LowStockAlert[];
}

export interface SeriesBreakdown {
  series: NoteSeries;
  totalValue: number;
  totalNotes: number;
  denominations: DenominationBreakdown[];
}

export interface DenominationBreakdown {
  denomination: NoteDenomination;
  series: NoteSeries;
  quantity: number;
  value: number;
  isLowStock: boolean;
}

export interface LowStockAlert {
  inventoryId: string;
  series: NoteSeries;
  denomination: NoteDenomination;
  currentQuantity: number;
  minimumThreshold: number;
  severity: AlertSeverity;
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface InventorySettings {
  lowStockThresholds: { [key: string]: number };
  autoReorderEnabled: boolean;
  reorderQuantities: { [key: string]: number };
}

export const NOTE_SERIES_LABELS: { [key in NoteSeries]: string } = {
  [NoteSeries.MANDELA]: 'Mandela Series',
  [NoteSeries.BIG_5]: 'Big 5 Series',
  [NoteSeries.COMMEMORATIVE]: 'Commemorative Series',
  [NoteSeries.V6]: 'V6 Series'
};

export const DENOMINATION_LABELS: { [key in NoteDenomination]: string } = {
  [NoteDenomination.R10]: 'R10',
  [NoteDenomination.R20]: 'R20',
  [NoteDenomination.R50]: 'R50',
  [NoteDenomination.R100]: 'R100',
  [NoteDenomination.R200]: 'R200'
};

export const COIN_DENOMINATION_LABELS: { [key in CoinDenomination]: string } = {
  [CoinDenomination.R5]: 'R5',
  [CoinDenomination.R2]: 'R2',
  [CoinDenomination.R1]: 'R1',
  [CoinDenomination.C50]: '50c',
  [CoinDenomination.C20]: '20c',
  [CoinDenomination.C10]: '10c'
};

// Coin batch configuration - coins per batch for each denomination
export const COIN_BATCH_CONFIG: { [key in CoinDenomination]: number } = {
  [CoinDenomination.R5]: 20,   // R5 - R100 - 20 coins - 1 batch
  [CoinDenomination.R2]: 25,   // R2 - R50 - 25 coins - 1 batch
  [CoinDenomination.R1]: 20,   // R1 - R20 - 20 coins - 1 batch
  [CoinDenomination.C50]: 20,  // 50c - R10 - 20 coins - 1 batch
  [CoinDenomination.C20]: 50,  // 20c - R10 - 50 coins - 1 batch
  [CoinDenomination.C10]: 100  // 10c - R10 - 100 coins - 1 batch
};

// Coin batch values - total value per batch for each denomination
export const COIN_BATCH_VALUES: { [key in CoinDenomination]: number } = {
  [CoinDenomination.R5]: 100,   // 20 × R5 = R100
  [CoinDenomination.R2]: 50,    // 25 × R2 = R50
  [CoinDenomination.R1]: 20,    // 20 × R1 = R20
  [CoinDenomination.C50]: 10,   // 20 × 50c = R10
  [CoinDenomination.C20]: 10,   // 50 × 20c = R10
  [CoinDenomination.C10]: 10    // 100 × 10c = R10
};

export const DEFAULT_LOW_STOCK_THRESHOLDS: { [key: string]: number } = {
  '10': 50,   // R10 notes
  '20': 40,   // R20 notes
  '50': 30,   // R50 notes
  '100': 20,  // R100 notes
  '200': 10   // R200 notes
};
