# Cash Management System - Domain Models

## Overview

This document outlines the comprehensive domain models for the Cash Management System, a secure Angular-based application designed for cash handling, distribution, and tracking within an organization. The system manages the complete lifecycle of cash requests from submission to completion with role-based access controls.

## Core Domain Entities

### 1. User Management Domain

#### User
The central entity representing system users with role-based access.

```typescript
interface User {
  id: string;                    // Unique identifier
  ab: string;                    // Employee identifier (AB number)
  fullName: string;              // Full name of the user
  email: string;                 // Email address
  contactNumber: string;         // Phone number
  role: UserRole;                // User's role in the system
  department: string;            // Department name
}
```

#### UserRole
Defines the different access levels and permissions within the system.

```typescript
enum UserRole {
  REQUESTER = 'requester',       // Can submit cash requests
  ISSUER = 'issuer',            // Can issue and receive cash
  MANAGER = 'manager',          // Can approve/reject requests
  ADMIN = 'admin'               // Full system administration
}
```

#### Department
Organizational units for user categorization.

```typescript
interface Department {
  id: string;                    // Unique identifier
  name: string;                  // Department name (e.g., APC, FFA, NCR)
}
```

### 2. Cash Request Domain

#### CashRequest
The primary entity representing a cash request throughout its lifecycle.

```typescript
interface CashRequest {
  // Basic Information
  id: string;                    // Unique request identifier
  requesterName: string;         // Name of person making request
  requesterId: string;           // ID of requester
  department: string;            // Requesting department
  
  // Cash Details
  bankNotes: BankNote[];         // Requested bank notes
  coins?: CoinRequest[];         // Optional coin requests
  coinsRequested?: boolean;      // Flag for coin inclusion
  dyePackRequired?: boolean;     // Security dye pack requirement
  
  // Dates and Timeline
  dateRequested: Date;           // When request was submitted
  expectedReturnDate?: Date;     // Expected return date
  actualReturnDate?: Date;       // Actual return date
  
  // Processing Information
  issuedBy?: string;             // Who issued the cash
  issuedTo?: string;             // Who received the cash
  cashCountedBeforeIssuance?: boolean;  // Pre-issuance verification
  cashCountedOnReturn?: boolean;        // Return verification
  cashReceivedBy?: string;       // Who received returned cash
  comments?: string;             // Additional notes
  
  // Status and Workflow
  status: RequestStatus;         // Current request status
  createdAt: Date;              // Creation timestamp
  updatedAt: Date;              // Last update timestamp
  
  // Rejection Details
  rejectionReason?: string;      // Reason for rejection
  rejectedBy?: string;           // Who rejected the request
  rejectedDate?: Date;           // When it was rejected
  isAutoRejected?: boolean;      // System auto-rejection flag
}
```

#### RequestStatus
Defines the workflow states of a cash request.

```typescript
enum RequestStatus {
  PENDING = 'pending',           // Awaiting approval
  APPROVED = 'approved',         // Approved, awaiting issuance
  ISSUED = 'issued',            // Cash has been issued
  RETURNED = 'returned',        // Cash has been returned
  COMPLETED = 'completed',      // Request fully completed
  CANCELLED = 'cancelled',      // Request cancelled
  REJECTED = 'rejected'         // Request rejected
}
```

#### BankNote
Represents requested bank note denominations.

```typescript
interface BankNote {
  denomination: NoteDenomination; // Note value
  quantity: number;               // Number of notes requested
  series?: NoteSeries;           // Optional series specification
}
```

#### CoinRequest
Represents requested coin denominations with batch-based quantities.

```typescript
interface CoinRequest {
  denomination: CoinDenomination; // Coin value
  batches: number;               // Number of batches
  quantity: number;              // Total coins (batches × coins per batch)
  value: number;                 // Total monetary value
}
```

### 3. Inventory Management Domain

#### CashInventory
Tracks available bank note inventory by series and denomination.

```typescript
interface CashInventory {
  id: string;                    // Unique inventory record ID
  noteSeries: NoteSeries;        // Note series type
  denomination: NoteDenomination; // Note value
  quantity: number;              // Available quantity
  value: number;                 // Total monetary value
  lastUpdated: Date;             // Last update timestamp
  updatedBy: string;             // Who last updated
}
```

#### CoinInventory
Tracks available coin inventory by denomination.

```typescript
interface CoinInventory {
  id: string;                    // Unique inventory record ID
  denomination: CoinDenomination; // Coin value
  quantity: number;              // Total coins available
  batches: number;               // Number of complete batches
  value: number;                 // Total monetary value
  lastUpdated: Date;             // Last update timestamp
  updatedBy: string;             // Who last updated
}
```

#### NoteSeries
South African Rand note series classifications.

```typescript
enum NoteSeries {
  MANDELA = 'mandela',           // Nelson Mandela series
  BIG_5 = 'big_5',              // Big 5 animal series
  COMMEMORATIVE = 'commemorative', // Special commemorative notes
  V6 = 'v6'                      // Version 6 series
}
```

#### NoteDenomination
Available bank note values.

```typescript
enum NoteDenomination {
  R10 = 10,                      // R10 notes
  R20 = 20,                      // R20 notes
  R50 = 50,                      // R50 notes
  R100 = 100,                    // R100 notes
  R200 = 200                     // R200 notes
}
```

#### CoinDenomination
Available coin values.

```typescript
enum CoinDenomination {
  R5 = 5,                        // R5 coins
  R2 = 2,                        // R2 coins
  R1 = 1,                        // R1 coins
  C50 = 0.5,                     // 50 cent coins
  C20 = 0.2,                     // 20 cent coins
  C10 = 0.1                      // 10 cent coins
}
```

### 4. Transaction and Audit Domain

#### InventoryTransaction
Records all inventory changes for audit purposes.

```typescript
interface InventoryTransaction {
  id: string;                    // Unique transaction ID
  type: TransactionType;         // Type of transaction
  inventoryId: string;           // Related inventory record
  quantityChange: number;        // Change in quantity (+/-)
  previousQuantity: number;      // Quantity before change
  newQuantity: number;           // Quantity after change
  reason: string;                // Reason for transaction
  performedBy: string;           // Who performed the transaction
  timestamp: Date;               // When transaction occurred
}
```

#### TransactionType
Types of inventory transactions.

```typescript
enum TransactionType {
  ADD = 'add',                   // Adding inventory
  REMOVE = 'remove',             // Removing inventory
  ADJUST = 'adjust',             // Manual adjustment
  ISSUE = 'issue',               // Cash issuance
  RETURN = 'return'              // Cash return
}
```

#### SystemLog
Comprehensive system activity logging.

```typescript
interface SystemLog {
  id: string;                    // Unique log entry ID
  type: LogType;                 // Type of log entry
  category: LogCategory;         // Log category
  severity: LogSeverity;         // Severity level
  title: string;                 // Log entry title
  message: string;               // Detailed message
  userId?: string;               // Associated user ID
  userName?: string;             // Associated user name
  requestId?: string;            // Associated request ID
  inventoryId?: string;          // Associated inventory ID
  metadata?: { [key: string]: any }; // Additional data
  timestamp: Date;               // When event occurred
  ipAddress?: string;            // User's IP address
  userAgent?: string;            // User's browser info
}
```

### 5. Notification and Communication Domain

#### Notification
System notifications for users.

```typescript
interface Notification {
  id: string;                    // Unique notification ID
  type: NotificationType;        // Type of notification
  title: string;                 // Notification title
  message: string;               // Notification content
  recipientId: string;           // Target user ID
  requestId?: string;            // Related request ID
  isRead: boolean;               // Read status
  createdAt: Date;               // Creation timestamp
  scheduledFor?: Date;           // Scheduled delivery time
}
```

#### NotificationType
Types of system notifications.

```typescript
enum NotificationType {
  NEW_REQUEST = 'new_request',           // New request submitted
  REQUEST_APPROVED = 'request_approved', // Request approved
  CASH_ISSUED = 'cash_issued',          // Cash issued
  RETURN_REMINDER = 'return_reminder',   // Return deadline reminder
  CASH_RETURNED = 'cash_returned',      // Cash returned
  REQUEST_COMPLETED = 'request_completed', // Request completed
  REQUEST_REJECTED = 'request_rejected', // Request rejected
  ISSUE_REPORTED = 'issue_reported',    // Issue reported
  ISSUE_RESOLVED = 'issue_resolved'     // Issue resolved
}
```

### 6. Issue Management Domain

#### Issue
Tracks problems and discrepancies in cash handling.

```typescript
interface Issue {
  id: string;                    // Unique issue ID
  title: string;                 // Issue title
  description: string;           // Detailed description
  category: IssueCategory;       // Issue category
  priority: IssuePriority;       // Priority level
  status: IssueStatus;           // Current status
  reportedBy: string;            // Reporter user ID
  reportedByName: string;        // Reporter name
  reportedAt: Date;              // Report timestamp
  assignedTo?: string;           // Assigned user ID
  assignedToName?: string;       // Assigned user name
  resolvedBy?: string;           // Resolver user ID
  resolvedByName?: string;       // Resolver name
  resolvedAt?: Date;             // Resolution timestamp
  resolution?: string;           // Resolution details
  requestId?: string;            // Related request ID
  attachments?: string[];        // File attachments
  comments?: IssueComment[];     // Issue comments
  metadata?: { [key: string]: any }; // Additional data
}
```

#### IssueComment
Comments and updates on issues.

```typescript
interface IssueComment {
  id: string;                    // Unique comment ID
  issueId: string;               // Parent issue ID
  userId: string;                // Commenter user ID
  userName: string;              // Commenter name
  comment: string;               // Comment text
  createdAt: Date;               // Comment timestamp
  isInternal?: boolean;          // Manager-only comment flag
}
```

#### IssueCategory
Categories of issues that can occur.

```typescript
enum IssueCategory {
  MISSING_NOTES = 'missing_notes',           // Notes missing from count
  DAMAGED_NOTES = 'damaged_notes',           // Damaged or destroyed notes
  COUNTERFEIT_NOTES = 'counterfeit_notes',   // Suspected counterfeit notes
  COUNTING_DISCREPANCY = 'counting_discrepancy', // Count doesn't match
  EQUIPMENT_MALFUNCTION = 'equipment_malfunction', // Equipment problems
  SECURITY_CONCERN = 'security_concern',     // Security-related issues
  PROCESS_VIOLATION = 'process_violation',   // Process not followed
  OTHER = 'other'                            // Other issues
}
```

#### IssuePriority
Priority levels for issue resolution.

```typescript
enum IssuePriority {
  LOW = 'low',                   // Low priority
  MEDIUM = 'medium',             // Medium priority
  HIGH = 'high',                 // High priority
  CRITICAL = 'critical'          // Critical priority
}
```

#### IssueStatus
Status of issue resolution.

```typescript
enum IssueStatus {
  OPEN = 'open',                 // Newly reported
  IN_PROGRESS = 'in_progress',   // Being worked on
  RESOLVED = 'resolved',         // Solution implemented
  CLOSED = 'closed'              // Officially closed
}
```

### 7. Analytics and Reporting Domain

#### InventorySummary
Comprehensive inventory overview.

```typescript
interface InventorySummary {
  totalValue: number;            // Total inventory value
  totalNotes: number;            // Total note count
  seriesBreakdown: SeriesBreakdown[];     // Breakdown by series
  denominationBreakdown: DenominationBreakdown[]; // Breakdown by denomination
  lowStockAlerts: LowStockAlert[];        // Current low stock alerts
}
```

#### SeriesBreakdown
Inventory breakdown by note series.

```typescript
interface SeriesBreakdown {
  series: NoteSeries;            // Note series
  totalValue: number;            // Total value for series
  totalNotes: number;            // Total notes for series
  denominations: DenominationBreakdown[]; // Denominations in series
}
```

#### DenominationBreakdown
Inventory breakdown by denomination.

```typescript
interface DenominationBreakdown {
  denomination: NoteDenomination; // Note denomination
  series: NoteSeries;            // Note series
  quantity: number;              // Available quantity
  value: number;                 // Total value
  isLowStock: boolean;           // Low stock flag
}
```

#### LowStockAlert
Alerts for low inventory levels.

```typescript
interface LowStockAlert {
  inventoryId: string;           // Related inventory ID
  series: NoteSeries;            // Note series
  denomination: NoteDenomination; // Note denomination
  currentQuantity: number;       // Current stock level
  minimumThreshold: number;      // Minimum required level
  severity: AlertSeverity;       // Alert severity
}
```

#### AlertSeverity
Severity levels for alerts.

```typescript
enum AlertSeverity {
  LOW = 'low',                   // Low severity
  MEDIUM = 'medium',             // Medium severity
  HIGH = 'high',                 // High severity
  CRITICAL = 'critical'          // Critical severity
}
```

#### LogSummary
Summary of system log activities.

```typescript
interface LogSummary {
  totalLogs: number;             // Total log entries
  criticalCount: number;         // Critical severity count
  errorCount: number;            // Error severity count
  warningCount: number;          // Warning severity count
  infoCount: number;             // Info severity count
  recentActivity: SystemLog[];   // Recent log entries
  topUsers: UserActivity[];      // Most active users
  categoryBreakdown: CategoryBreakdown[]; // Activity by category
}
```

#### AuditReport
Comprehensive audit reporting.

```typescript
interface AuditReport {
  id: string;                    // Unique report ID
  title: string;                 // Report title
  description: string;           // Report description
  generatedBy: string;           // Report generator
  generatedAt: Date;             // Generation timestamp
  period: {                      // Report period
    startDate: Date;
    endDate: Date;
  };
  summary: LogSummary;           // Activity summary
  logs: SystemLog[];             // Included log entries
  statistics: AuditStatistics;   // Statistical analysis
  recommendations: string[];     // Recommendations
}
```

#### AuditStatistics
Statistical analysis for audit reports.

```typescript
interface AuditStatistics {
  totalRequests: number;         // Total requests in period
  completedRequests: number;     // Successfully completed requests
  lateReturns: number;           // Late return count
  discrepancies: number;         // Discrepancy count
  averageProcessingTime: number; // Average processing time
  complianceScore: number;       // Compliance score (0-100)
  riskLevel: RiskLevel;          // Overall risk assessment
}
```

### 8. Validation and Business Rules Domain

#### InventoryValidationResult
Results of inventory validation checks.

```typescript
interface InventoryValidationResult {
  isValid: boolean;              // Overall validation result
  errors: string[];              // Validation errors
  warnings: string[];            // Validation warnings
  inventoryPreview?: InventoryPreviewItem[]; // Preview of changes
}
```

#### InventoryPreviewItem
Preview of inventory changes before approval.

```typescript
interface InventoryPreviewItem {
  denomination: NoteDenomination; // Note denomination
  currentQuantity: number;       // Current inventory
  requestedQuantity: number;     // Requested amount
  remainingAfterApproval: number; // Remaining after approval
  series: string;                // Note series
}
```

#### RejectionResult
Results of automatic rejection analysis.

```typescript
interface RejectionResult {
  isRejected: boolean;           // Whether request should be rejected
  reason: string;                // Rejection reason
  insufficientDenominations: {   // Insufficient inventory details
    denomination: NoteDenomination;
    requested: number;
    available: number;
    shortage: number;
  }[];
  suggestedAlternatives?: string[]; // Alternative suggestions
}
```

#### InventoryAvailability
Availability status for denominations.

```typescript
interface InventoryAvailability {
  denomination: NoteDenomination; // Note denomination
  status: InventoryStatus;       // Availability status
  totalAvailable?: number;       // Total available (approvers only)
}
```

#### SeriesInventoryAvailability
Availability by series and denomination.

```typescript
interface SeriesInventoryAvailability {
  denomination: NoteDenomination; // Note denomination
  series: NoteSeries;            // Note series
  status: InventoryStatus;       // Availability status
  available: number;             // Available quantity
  isRecommended?: boolean;       // Recommended series flag
}
```

#### InventoryStatus
Status indicators for inventory availability.

```typescript
enum InventoryStatus {
  AVAILABLE = 'available',       // Sufficient stock
  LOW_STOCK = 'low_stock',      // Low stock warning
  OUT_OF_STOCK = 'out_of_stock' // No stock available
}
```

### 9. Configuration and Settings Domain

#### InventorySettings
System configuration for inventory management.

```typescript
interface InventorySettings {
  lowStockThresholds: { [key: string]: number }; // Minimum stock levels
  autoReorderEnabled: boolean;   // Auto-reorder feature
  reorderQuantities: { [key: string]: number };  // Reorder amounts
}
```

#### NotificationSettings
User notification preferences.

```typescript
interface NotificationSettings {
  emailNotifications: boolean;   // Email notification preference
  reminderMinutes: number;       // Minutes before deadline for reminders
}
```

### 10. Summary and Helper Types

#### CashRequestSummary
Summary information for cash requests.

```typescript
interface CashRequestSummary {
  totalAmount: number;           // Total monetary value
  noteBreakdown: BankNote[];     // Note breakdown
  coinBreakdown?: CoinRequest[]; // Coin breakdown (optional)
  totalCoinValue?: number;       // Total coin value (optional)
}
```

#### IssueSummary
Summary of issue management metrics.

```typescript
interface IssueSummary {
  totalIssues: number;           // Total issues
  openIssues: number;            // Open issues
  inProgressIssues: number;      // In-progress issues
  resolvedIssues: number;        // Resolved issues
  closedIssues: number;          // Closed issues
  criticalIssues: number;        // Critical priority issues
  highPriorityIssues: number;    // High priority issues
  averageResolutionTime: number; // Average resolution time (hours)
  categoryBreakdown: { category: IssueCategory; count: number }[]; // Issues by category
}
```

## Domain Relationships

### Core Entity Relationship Diagram

```mermaid
erDiagram
    User {
        string id PK
        string ab
        string fullName
        string email
        string contactNumber
        UserRole role
        string department
    }

    Department {
        string id PK
        string name
    }

    CashRequest {
        string id PK
        string requesterName
        string requesterId FK
        string department
        Date dateRequested
        Date expectedReturnDate
        Date actualReturnDate
        string issuedBy FK
        string issuedTo FK
        string cashReceivedBy FK
        RequestStatus status
        Date createdAt
        Date updatedAt
        string rejectionReason
        string rejectedBy FK
        Date rejectedDate
        boolean isAutoRejected
        string comments
        boolean dyePackRequired
        boolean coinsRequested
        boolean cashCountedBeforeIssuance
        boolean cashCountedOnReturn
    }

    BankNote {
        string id PK
        string requestId FK
        NoteDenomination denomination
        number quantity
        NoteSeries series
    }

    CoinRequest {
        string id PK
        string requestId FK
        CoinDenomination denomination
        number batches
        number quantity
        number value
    }

    CashInventory {
        string id PK
        NoteSeries noteSeries
        NoteDenomination denomination
        number quantity
        number value
        Date lastUpdated
        string updatedBy FK
    }

    CoinInventory {
        string id PK
        CoinDenomination denomination
        number quantity
        number batches
        number value
        Date lastUpdated
        string updatedBy FK
    }

    InventoryTransaction {
        string id PK
        TransactionType type
        string inventoryId FK
        number quantityChange
        number previousQuantity
        number newQuantity
        string reason
        string performedBy FK
        Date timestamp
    }

    Notification {
        string id PK
        NotificationType type
        string title
        string message
        string recipientId FK
        string requestId FK
        boolean isRead
        Date createdAt
        Date scheduledFor
    }

    Issue {
        string id PK
        string title
        string description
        IssueCategory category
        IssuePriority priority
        IssueStatus status
        string reportedBy FK
        string reportedByName
        Date reportedAt
        string assignedTo FK
        string assignedToName
        string resolvedBy FK
        string resolvedByName
        Date resolvedAt
        string resolution
        string requestId FK
    }

    IssueComment {
        string id PK
        string issueId FK
        string userId FK
        string userName
        string comment
        Date createdAt
        boolean isInternal
    }

    SystemLog {
        string id PK
        LogType type
        LogCategory category
        LogSeverity severity
        string title
        string message
        string userId FK
        string userName
        string requestId FK
        string inventoryId FK
        Date timestamp
        string ipAddress
        string userAgent
    }

    %% Primary Relationships
    User ||--o{ CashRequest : "creates"
    User ||--|| Department : "belongs_to"
    User ||--o{ InventoryTransaction : "performs"
    User ||--o{ Notification : "receives"
    User ||--o{ Issue : "reports"
    User ||--o{ Issue : "assigned_to"
    User ||--o{ Issue : "resolves"
    User ||--o{ IssueComment : "writes"
    User ||--o{ SystemLog : "generates"
    User ||--o{ CashInventory : "updates"
    User ||--o{ CoinInventory : "updates"

    CashRequest ||--o{ BankNote : "contains"
    CashRequest ||--o{ CoinRequest : "contains"
    CashRequest ||--o{ Notification : "triggers"
    CashRequest ||--o{ Issue : "has"
    CashRequest ||--o{ SystemLog : "logs"

    CashInventory ||--o{ InventoryTransaction : "tracked_by"
    CoinInventory ||--o{ InventoryTransaction : "tracked_by"

    Issue ||--o{ IssueComment : "has"
```

### Cash Request Workflow Diagram

```mermaid
stateDiagram-v2
    [*] --> PENDING : User submits request

    PENDING --> APPROVED : Manager approves
    PENDING --> REJECTED : Manager rejects
    PENDING --> REJECTED : Auto-rejection (insufficient inventory)
    PENDING --> CANCELLED : User cancels

    APPROVED --> ISSUED : Issuer processes cash
    APPROVED --> CANCELLED : Request cancelled

    ISSUED --> RETURNED : Cash returned
    ISSUED --> OVERDUE : Past return date

    RETURNED --> COMPLETED : Process completed
    OVERDUE --> RETURNED : Late return
    OVERDUE --> ISSUE_REPORTED : Discrepancy found

    REJECTED --> [*]
    CANCELLED --> [*]
    COMPLETED --> [*]
    ISSUE_REPORTED --> COMPLETED : Issue resolved

    note right of PENDING
        Validation checks:
        - Inventory availability
        - User permissions
        - Request limits
    end note

    note right of APPROVED
        Ready for cash issuance
        Inventory reserved
    end note

    note right of ISSUED
        Cash handed out
        Return deadline set
        Notifications sent
    end note

    note right of RETURNED
        Cash counted and verified
        Inventory updated
    end note
```

### User Role Access Control Diagram

```mermaid
graph TD
    A[User Authentication] --> B{Role Check}

    B -->|REQUESTER| C[Requester Dashboard]
    B -->|ISSUER| D[Issuer Dashboard]
    B -->|MANAGER| E[Manager Dashboard]
    B -->|ADMIN| F[Admin Dashboard]

    C --> C1[Submit Cash Requests]
    C --> C2[View Own Requests]
    C --> C3[Cancel Pending Requests]
    C --> C4[Report Issues]

    D --> D1[View Approved Requests]
    D --> D2[Issue Cash]
    D --> D3[Process Returns]
    D --> D4[Update Inventory]
    D --> D5[Report Issues]

    E --> E1[Approve/Reject Requests]
    E --> E2[View All Requests]
    E --> E3[Manage Inventory]
    E --> E4[Generate Reports]
    E --> E5[Resolve Issues]
    E --> E6[View System Logs]

    F --> F1[User Management]
    F --> F2[System Configuration]
    F --> F3[Audit Reports]
    F --> F4[All Manager Functions]

    style C fill:#e1f5fe
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

### Inventory Management Flow Diagram

```mermaid
flowchart TD
    A[Cash Request Submitted] --> B{Inventory Check}

    B -->|Sufficient| C[Reserve Inventory]
    B -->|Insufficient| D[Auto-Reject Request]

    C --> E[Request Approved]
    D --> F[Send Rejection Notification]

    E --> G[Cash Issued]
    G --> H[Update Inventory - Decrease]
    H --> I[Set Return Deadline]

    I --> J{Cash Returned?}
    J -->|Yes| K[Count & Verify Cash]
    J -->|No - Overdue| L[Send Overdue Notification]

    K --> M{Count Matches?}
    M -->|Yes| N[Update Inventory - Increase]
    M -->|No| O[Create Discrepancy Issue]

    N --> P[Complete Request]
    O --> Q[Investigation Process]
    Q --> R{Issue Resolved?}
    R -->|Yes| P
    R -->|No| S[Escalate to Management]

    L --> T{Eventually Returned?}
    T -->|Yes| K
    T -->|No| U[Create Missing Cash Issue]

    F --> V[End Process]
    P --> V
    S --> V
    U --> V

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style D fill:#ffebee
    style G fill:#e8f5e8
    style O fill:#fff3e0
    style U fill:#ffebee
```

### Notification System Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant S as System
    participant M as Manager
    participant I as Issuer
    participant N as Notification Service

    U->>S: Submit Cash Request
    S->>N: Create NEW_REQUEST notification
    N->>M: Notify Manager (approval needed)
    N->>I: Notify Issuer (new request)

    M->>S: Approve Request
    S->>N: Create REQUEST_APPROVED notification
    N->>U: Notify Requester (approved)
    N->>I: Notify Issuer (ready for issuance)

    I->>S: Issue Cash
    S->>N: Create CASH_ISSUED notification
    N->>U: Notify Requester (cash issued)
    N->>M: Notify Manager (cash issued)

    Note over S,N: Return deadline approaches
    S->>N: Create RETURN_REMINDER notification
    N->>U: Remind about return deadline

    U->>S: Return Cash
    S->>N: Create CASH_RETURNED notification
    N->>I: Notify Issuer (cash returned)
    N->>M: Notify Manager (cash returned)

    S->>N: Create REQUEST_COMPLETED notification
    N->>U: Notify Requester (completed)
```

### Issue Management Workflow Diagram

```mermaid
stateDiagram-v2
    [*] --> OPEN : Issue reported

    OPEN --> IN_PROGRESS : Assigned to resolver
    OPEN --> CLOSED : Duplicate/Invalid

    IN_PROGRESS --> RESOLVED : Solution implemented
    IN_PROGRESS --> OPEN : Needs more info

    RESOLVED --> CLOSED : Verified by reporter
    RESOLVED --> IN_PROGRESS : Solution inadequate

    CLOSED --> [*]

    note right of OPEN
        Categories:
        - Missing Notes
        - Damaged Notes
        - Counterfeit Notes
        - Counting Discrepancy
        - Equipment Malfunction
        - Security Concern
        - Process Violation
    end note

    note right of IN_PROGRESS
        Priorities:
        - CRITICAL (immediate)
        - HIGH (same day)
        - MEDIUM (within week)
        - LOW (when possible)
    end note
```

### System Architecture Overview Diagram

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[User Interface Components]
        A1[Dashboard Components]
        A2[Form Components]
        A3[Modal Components]
        A4[Notification Components]
    end

    subgraph "Business Logic Layer"
        B[Services]
        B1[User Service]
        B2[Cash Request Service]
        B3[Inventory Service]
        B4[Notification Service]
        B5[System Log Service]
        B6[Issue Service]
    end

    subgraph "Data Layer"
        C[Local Storage Service]
        C1[User Data]
        C2[Request Data]
        C3[Inventory Data]
        C4[Notification Data]
        C5[Log Data]
        C6[Issue Data]
    end

    subgraph "Domain Models"
        D[TypeScript Interfaces]
        D1[User Models]
        D2[Request Models]
        D3[Inventory Models]
        D4[Notification Models]
        D5[Log Models]
        D6[Issue Models]
    end

    A --> B
    A1 --> B1
    A1 --> B2
    A2 --> B2
    A2 --> B3
    A3 --> B3
    A4 --> B4

    B --> C
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    B5 --> C5
    B6 --> C6

    B --> D
    C --> D

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

### Data Flow Architecture Diagram

```mermaid
graph LR
    subgraph "User Actions"
        UA1[Submit Request]
        UA2[Approve Request]
        UA3[Issue Cash]
        UA4[Return Cash]
        UA5[Report Issue]
    end

    subgraph "System Processing"
        SP1[Validate Request]
        SP2[Check Inventory]
        SP3[Update Status]
        SP4[Generate Notifications]
        SP5[Log Activity]
        SP6[Update Inventory]
    end

    subgraph "Data Storage"
        DS1[(Cash Requests)]
        DS2[(Inventory)]
        DS3[(Users)]
        DS4[(Notifications)]
        DS5[(System Logs)]
        DS6[(Issues)]
    end

    subgraph "Business Rules Engine"
        BR1{Inventory Available?}
        BR2{User Authorized?}
        BR3{Return Overdue?}
        BR4{Count Matches?}
    end

    UA1 --> SP1
    SP1 --> BR2
    BR2 -->|Yes| SP2
    BR2 -->|No| SP5

    SP2 --> BR1
    BR1 -->|Yes| SP3
    BR1 -->|No| SP3

    SP3 --> SP4
    SP4 --> SP5
    SP5 --> DS5

    UA2 --> SP3
    UA3 --> SP6
    SP6 --> DS2

    UA4 --> BR4
    BR4 -->|Yes| SP6
    BR4 -->|No| UA5

    UA5 --> DS6

    SP1 --> DS1
    SP3 --> DS1
    SP4 --> DS4

    DS1 -.-> BR3
    BR3 -->|Yes| SP4

    style UA1 fill:#e3f2fd
    style UA2 fill:#e8f5e8
    style UA3 fill:#fff3e0
    style UA4 fill:#f3e5f5
    style UA5 fill:#ffebee
    style BR1 fill:#fff9c4
    style BR2 fill:#fff9c4
    style BR3 fill:#fff9c4
    style BR4 fill:#fff9c4
```

### Business Rules and Constraints

#### 1. Cash Request Workflow Rules

```mermaid
graph TD
    A[Request Submitted] --> B{Validation Rules}

    B --> B1[User must be authenticated]
    B --> B2[User role must allow requests]
    B --> B3[Request amount within limits]
    B --> B4[Valid denominations only]
    B --> B5[Return date must be future]

    B1 & B2 & B3 & B4 & B5 --> C{All Rules Pass?}

    C -->|Yes| D[Status: PENDING]
    C -->|No| E[Status: REJECTED]

    D --> F{Manager Approval}
    F -->|Approved| G{Inventory Check}
    F -->|Rejected| H[Status: REJECTED]

    G -->|Available| I[Status: APPROVED]
    G -->|Insufficient| J[Status: REJECTED - Auto]

    I --> K[Ready for Issuance]
    K --> L[Status: ISSUED]
    L --> M[Return Deadline Set]

    M --> N{Returned On Time?}
    N -->|Yes| O[Status: RETURNED]
    N -->|No| P[Status: OVERDUE]

    O --> Q{Count Verified?}
    Q -->|Yes| R[Status: COMPLETED]
    Q -->|No| S[Create Issue]

    P --> T{Eventually Returned?}
    T -->|Yes| Q
    T -->|No| U[Create Missing Cash Issue]
```

#### 2. Role-Based Access Control Rules

- **REQUESTER**: Can submit, view own requests, cancel pending requests
- **ISSUER**: Can view approved requests, issue cash, process returns, update inventory
- **MANAGER**: Can approve/reject requests, view all requests, manage inventory, generate reports
- **ADMIN**: Full system access, user management, system configuration

#### 3. Inventory Validation Rules

- Requests must not exceed available inventory
- Low stock alerts triggered at configurable thresholds
- Series preference applied when multiple series available
- Coin requests validated against batch configurations

#### 4. Audit Trail Requirements

- All user actions logged with timestamp and user ID
- Inventory changes tracked with before/after quantities
- Request status changes recorded with reasons
- System events logged for security and compliance

#### 5. Notification System Rules

- Automatic notifications for all status changes
- Reminder notifications before return deadlines
- Escalation notifications for overdue returns
- Issue notifications to relevant stakeholders

#### 6. Security Control Rules

- Dye pack tracking for high-value requests
- Cash counting verification before issuance and after return
- Dual authorization for high-value transactions
- Secure audit logging for all cash handling activities

#### 7. Return Deadline Management

- Expected return dates mandatory for all issued cash
- Automatic reminder notifications 24 hours before deadline
- Overdue status triggered after deadline passes
- Escalation process for extended overdue periods

#### 8. Issue Management Rules

- Issues automatically created for counting discrepancies
- Priority assignment based on issue category and value
- Mandatory resolution for critical and high-priority issues
- Audit trail maintained for all issue activities

## Technical Implementation Notes

- All entities use string-based UUIDs for unique identification
- Timestamps use JavaScript Date objects
- Enums provide type safety and consistency
- Optional fields marked with `?` for flexible data modeling
- Metadata fields allow for extensibility
- Audit fields track who and when for accountability

This comprehensive domain model provides the foundation for a secure, auditable, and efficient cash management system that supports the complete lifecycle of cash operations within an organization.

## 🎨 Interactive Visualization

For an interactive, draggable visualization of these domain models, see the **Domain Models** folder which contains:
- `index.html` - Interactive domain model visualizer
- `styles.css` - Modern styling and animations
- `script.js` - Drag-and-drop functionality and interactions

The visualizer allows you to:
- Drag and rearrange entities
- View detailed entity information
- Explore relationships interactively
- Filter by domain categories
- Export diagrams
- Edit entity properties
