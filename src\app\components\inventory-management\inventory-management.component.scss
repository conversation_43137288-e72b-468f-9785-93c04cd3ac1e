.inventory-management {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

// Modern Navigation Toolbar
.app-toolbar {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .back-button {
    margin-right: 1rem;
    color: white;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255,255,255,0.1);
      transform: scale(1.05);
    }
  }

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;

    &.clickable-logo {
      cursor: pointer;
    }

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    margin-left: 16px;
    font-size: 1.2rem;
    font-weight: 500;
    color: white;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;

    .add-cash-btn {
      border-radius: 20px;
      font-weight: 600;
      text-transform: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      transition: all 0.3s ease;

      .btn-text {
        margin-left: 0.5rem;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      }

      @media (max-width: 768px) {
        .btn-text {
          display: none;
        }
      }
    }

    .more-actions {
      color: white;

      &:hover {
        background-color: rgba(255,255,255,0.1);
      }
    }
  }

  button {
    color: white;
  }
}

// Content Container
.content-container {
  padding: 1rem 2rem 0.5rem 2rem;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  background: transparent;

  // Large screens - utilize more space
  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 1rem 3rem 0.5rem 3rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1600px;
    padding: 1rem 2.5rem 0.5rem 2.5rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    max-width: 1200px;
    padding: 1rem 2rem 0.5rem 2rem;
  }

  @media (max-width: 768px) {
    padding: 1rem 1rem 0.5rem 1rem;
  }
}

// Modern Hero Section
.hero-section {
  margin-bottom: 2rem;

  .hero-header {
    text-align: center;
    margin-bottom: 2rem;

    .hero-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--absa-dark-blue);
      margin: 0 0 0.5rem 0;
      letter-spacing: -0.5px;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .hero-subtitle {
      font-size: 1.1rem;
      color: var(--absa-gray-medium);
      margin: 0;
      font-weight: 400;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }
}

// Modern Summary Grid
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 4 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Summary Cards
.modern-summary-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.08;
    transform: translate(40px, -40px);
  }

  .card-content {
    position: relative;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      mat-icon {
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        color: white;
      }

      .alert-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 20px;
        height: 20px;
        background-color: #CC0000;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;

      &.positive {
        background: rgba(76, 175, 80, 0.1);
        color: #4CAF50;
      }

      &.neutral {
        background: rgba(158, 158, 158, 0.1);
        color: #9E9E9E;
      }

      &.warning {
        background: rgba(255, 152, 0, 0.1);
        color: #FF9800;
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }

  .card-body {
    .primary-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--absa-dark-blue);
      line-height: 1;
      margin-bottom: 0.5rem;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .card-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--absa-dark-blue);
      margin-bottom: 0.25rem;
    }

    .card-description {
      font-size: 0.875rem;
      color: var(--absa-gray-medium);
      font-weight: 500;
    }
  }

  // Card-specific styling
  &.value-card {
    .card-background {
      background: var(--absa-green);
    }
    .card-icon {
      background: linear-gradient(135deg, var(--absa-green) 0%, #388e3c 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: var(--absa-light-blue);
    }
    .card-icon {
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
    }
  }

  &.alerts-card {
    .card-background {
      background: var(--absa-gold);
    }
    .card-icon {
      background: linear-gradient(135deg, var(--absa-gold) 0%, #f57c00 100%);
    }

    &.critical {
      .card-background {
        background: #f44336;
      }
      .card-icon {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      }
    }
  }

  &.series-card {
    .card-background {
      background: var(--absa-dark-blue);
    }
    .card-icon {
      background: linear-gradient(135deg, var(--absa-dark-blue) 0%, var(--absa-light-blue) 100%);
    }
  }
}

// Modern Inventory Section
.inventory-section {
  .inventory-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
  }

  .section-header {
    background: linear-gradient(135deg, var(--absa-dark-blue) 0%, var(--absa-light-blue) 100%);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
      padding: 1.5rem;
    }

    .section-title {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: white !important;
      letter-spacing: 0.5px;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .section-subtitle {
      font-size: 1rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 500;
      color: white !important;
    }

    .section-actions {
      .bulk-action-btn {
        border-color: white;
        color: white;
        border-radius: 20px;
        font-weight: 600;
        text-transform: none;

        &:hover {
          background-color: rgba(255,255,255,0.1);
        }
      }
    }
  }

  .inventory-content {
    padding: 0;

    .modern-tabs {
      ::ng-deep .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: 1px solid rgba(0,0,0,0.1);
          background: #f8f9fa;

          .mat-mdc-tab {
            min-width: 160px;
            font-weight: 600;
            color: var(--absa-dark-blue);
            text-transform: none;

            &.mdc-tab--active {
              color: var(--absa-red);
            }
          }

          .mat-ink-bar {
            background-color: var(--absa-red);
            height: 3px;
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
            }
          }
        }
      }
    }
  }
}

// Tab Content
.tab-content {
  padding: 2rem;
  background: white;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// Series Overview
.series-overview {
  margin-bottom: 2rem;

  .series-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }
  }

  .series-stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid rgba(0,0,0,0.05);

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        color: white;
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--absa-dark-blue);
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--absa-gray-medium);
        font-weight: 500;
      }
    }
  }
}

// Modern Inventory Grid
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.75rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Inventory Item Cards
.inventory-item {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    .denomination-info {
      display: flex;
      align-items: center;
      gap: 1rem;

      .denomination-icon {
        width: 80px;
        height: 50px;
        border-radius: 8px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 2px solid rgba(255,255,255,0.8);

        .note-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 6px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }

        .fallback-icon {
          font-size: 1.75rem;
          width: 1.75rem;
          height: 1.75rem;
          color: var(--absa-green);
        }

        // Add a subtle border effect for better visual appeal
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 8px;
          background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
          pointer-events: none;
        }
      }

      .denomination-details {
        flex: 1;

        .denomination-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--absa-dark-blue);
          margin: 0 0 0.25rem 0;
        }

        .denomination-series {
          font-size: 0.875rem;
          color: var(--absa-gray-medium);
          margin: 0 0 0.25rem 0;
          font-weight: 500;
        }

        .denomination-value {
          font-size: 0.75rem;
          font-weight: 600;
          color: var(--absa-green);
          background: rgba(76, 175, 80, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          display: inline-block;
        }
      }
    }

    .item-status {
      mat-chip {
        border-radius: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .item-body {
    margin-bottom: 1rem;

    .item-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .metric {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;

        .metric-value {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--absa-dark-blue);
          line-height: 1;
          margin-bottom: 0.25rem;
        }

        .metric-label {
          font-size: 0.875rem;
          color: var(--absa-gray-medium);
          font-weight: 500;
        }
      }
    }

    .item-actions {
      text-align: center;

      .add-btn {
        border-radius: 20px;
        font-weight: 600;
        text-transform: none;
        padding: 0.75rem 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
      }
    }
  }

  .stock-progress {
    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.5rem;

      span {
        font-size: 0.875rem;
        color: var(--absa-gray-medium);
        font-weight: 500;
      }
    }
  }
}

// Enhanced Status Chips
mat-chip {
  &.status-in-stock {
    background-color: var(--absa-green) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  }

  &.status-watch {
    background-color: var(--absa-light-blue) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
  }

  &.status-medium {
    background-color: var(--absa-gold) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
  }

  &.status-low {
    background-color: #ff9800 !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
  }

  &.status-critical {
    background-color: #f44336 !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
  }

  &.status-unknown {
    background-color: var(--absa-gray-medium) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(158, 158, 158, 0.3);
  }
}

// Modern No Data State
.no-data {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin: 2rem 0;

  .no-data-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem auto;
    background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: white;
    }
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--absa-dark-blue);
    margin: 0 0 0.5rem 0;
  }

  p {
    font-size: 1rem;
    color: var(--absa-gray-medium);
    margin: 0 0 2rem 0;
    font-weight: 500;
  }

  button {
    border-radius: 20px;
    font-weight: 600;
    text-transform: none;
    padding: 0.75rem 2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0,0,0,0.2);
    }
  }
}

// Series-Specific Styling
.inventory-item {
  &.mandela-series {
    border-left: 4px solid #4CAF50;

    .denomination-icon {
      border-color: rgba(76, 175, 80, 0.3);
    }
  }

  &.big5-series {
    border-left: 4px solid #FF9800;

    .denomination-icon {
      border-color: rgba(255, 152, 0, 0.3);
    }
  }

  &.commemorative-series {
    border-left: 4px solid #9C27B0;

    .denomination-icon {
      border-color: rgba(156, 39, 176, 0.3);
    }
  }

  &.v6-series {
    border-left: 4px solid var(--absa-light-blue);

    .denomination-icon {
      border-color: rgba(33, 150, 243, 0.3);
    }
  }
}

// Enhanced fallback icon styling
.fallback-icon {
  display: none;
  color: var(--absa-green);
  opacity: 0.7;
}
