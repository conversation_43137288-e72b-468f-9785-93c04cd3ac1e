/* Cash Management System - Domain Models Visualizer Styles */

/* CSS Variables for consistent theming */
:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;

  /* Domain Colors */
  --user-color: #8b5cf6;
  --request-color: #06b6d4;
  --inventory-color: #10b981;
  --transaction-color: #f59e0b;
  --notification-color: #ec4899;
  --issue-color: #ef4444;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Layout */
  --header-height: 4rem;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  overflow-x: hidden;
}

/* App Container */
.app-container {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  height: 100vh;
  width: 100vw;
}

/* Header Styles */
.header {
  grid-area: header;
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-xl);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.logo h1 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.subtitle {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  font-weight: 500;
}

.header-controls {
  display: flex;
  gap: var(--spacing-md);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

/* Sidebar Styles */
.sidebar {
  grid-area: sidebar;
  background: var(--white);
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  overflow-y: auto;
  z-index: 900;
}

.sidebar-content {
  padding: var(--spacing-lg);
}

.sidebar h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-lg);
}

.sidebar h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-700);
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
}

/* Filter Styles */
.filter-group {
  margin-bottom: var(--spacing-lg);
}

.filter-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  cursor: pointer;
  transition: color var(--transition-fast);
  user-select: none;
}

.filter-label:hover {
  color: var(--primary-color);
}

.filter-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.filter-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.filter-label input[type="checkbox"]:checked + .checkmark::after {
  opacity: 1;
}

/* Domain-specific checkmark colors */
.checkmark.user:checked { background: var(--user-color); border-color: var(--user-color); }
.checkmark.request:checked { background: var(--request-color); border-color: var(--request-color); }
.checkmark.inventory:checked { background: var(--inventory-color); border-color: var(--inventory-color); }
.checkmark.transaction:checked { background: var(--transaction-color); border-color: var(--transaction-color); }
.checkmark.notification:checked { background: var(--notification-color); border-color: var(--notification-color); }
.checkmark.issue:checked { background: var(--issue-color); border-color: var(--issue-color); }

/* Legend Styles */
.legend {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.legend-color.user { background: var(--user-color); }
.legend-color.request { background: var(--request-color); }
.legend-color.inventory { background: var(--inventory-color); }
.legend-color.transaction { background: var(--transaction-color); }
.legend-color.notification { background: var(--notification-color); }
.legend-color.issue { background: var(--issue-color); }

/* Stats Styles */
.stats {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.stat-value {
  font-weight: 600;
  color: var(--gray-900);
  background: var(--gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

/* Main Content Styles */
.main-content {
  grid-area: main;
  position: relative;
  overflow: hidden;
  background: var(--gray-50);
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  background:
    radial-gradient(circle at 25px 25px, var(--gray-300) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, var(--gray-300) 2px, transparent 0);
  background-size: 100px 100px;
  background-position: 0 0, 50px 50px;
}

.relationship-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.entities-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  min-width: 100vw;
  z-index: 2;
}

/* Entity Styles */
.entity {
  position: absolute;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  cursor: move;
  transition: all var(--transition-normal);
  user-select: none;
  min-width: 200px;
  max-width: 280px;
  border-left: 4px solid var(--gray-300);
}

.entity:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.entity.dragging {
  transform: rotate(2deg) scale(1.05);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
}

.entity.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.8);
}

/* Domain-specific entity colors */
.entity.user { border-left-color: var(--user-color); }
.entity.request { border-left-color: var(--request-color); }
.entity.inventory { border-left-color: var(--inventory-color); }
.entity.transaction { border-left-color: var(--transaction-color); }
.entity.notification { border-left-color: var(--notification-color); }
.entity.issue { border-left-color: var(--issue-color); }

.entity-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.entity-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.entity-icon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.entity.user .entity-icon { background: var(--user-color); }
.entity.request .entity-icon { background: var(--request-color); }
.entity.inventory .entity-icon { background: var(--inventory-color); }
.entity.transaction .entity-icon { background: var(--transaction-color); }
.entity.notification .entity-icon { background: var(--notification-color); }
.entity.issue .entity-icon { background: var(--issue-color); }

.entity-type {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.entity-body {
  padding: var(--spacing-md);
}

.entity-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
}

.entity-properties {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.property-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.property-item i {
  width: 12px;
  text-align: center;
  color: var(--gray-400);
}

.entity-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.relationship-count {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.view-details-btn {
  font-size: var(--font-size-xs);
  color: var(--primary-color);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

.view-details-btn:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Relationship Lines */
.relationship-line {
  stroke: var(--gray-400);
  stroke-width: 2;
  fill: none;
  opacity: 0.7;
  transition: all var(--transition-fast);
}

.relationship-line:hover {
  stroke: var(--primary-color);
  stroke-width: 3;
  opacity: 1;
}

.relationship-line.highlighted {
  stroke: var(--primary-color);
  stroke-width: 3;
  opacity: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.relationship-arrow {
  fill: var(--gray-400);
  transition: fill var(--transition-fast);
}

.relationship-line:hover + .relationship-arrow,
.relationship-line.highlighted + .relationship-arrow {
  fill: var(--primary-color);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-normal);
}

.modal.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.modal-header h2 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-xl);
  max-height: 60vh;
  overflow-y: auto;
}

.entity-info {
  display: grid;
  gap: var(--spacing-xl);
}

.info-section h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.info-section h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--primary-color);
  border-radius: var(--radius-sm);
}

.properties-list {
  display: grid;
  gap: var(--spacing-sm);
}

.property-row {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  align-items: center;
}

.property-name {
  font-weight: 500;
  color: var(--gray-700);
}

.property-type {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  background: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.property-required {
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.property-required.required {
  background: var(--error-color);
  color: var(--white);
}

.property-required.optional {
  background: var(--gray-200);
  color: var(--gray-600);
}

.relationships-list {
  display: grid;
  gap: var(--spacing-sm);
}

.relationship-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

.relationship-item:hover {
  background: var(--gray-100);
}

.relationship-type {
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.relationship-type.one-to-one {
  background: var(--info-color);
  color: var(--white);
}

.relationship-type.one-to-many {
  background: var(--success-color);
  color: var(--white);
}

.relationship-type.many-to-many {
  background: var(--warning-color);
  color: var(--white);
}

.relationship-description {
  flex: 1;
  color: var(--gray-700);
}

.rules-list {
  display: grid;
  gap: var(--spacing-sm);
}

.rule-item {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.rule-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.rule-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  text-align: center;
  color: var(--gray-600);
}

.loading-spinner i {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.loading-spinner p {
  font-size: var(--font-size-lg);
  font-weight: 500;
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  z-index: 4000;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  border-left: 4px solid var(--success-color);
}

.toast.active {
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-content i {
  color: var(--success-color);
  font-size: var(--font-size-lg);
}

.toast.error {
  border-left-color: var(--error-color);
}

.toast.error .toast-content i {
  color: var(--error-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

.toast.warning .toast-content i {
  color: var(--warning-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-container {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr;
  }

  .sidebar {
    width: var(--sidebar-collapsed-width);
    overflow: visible;
  }

  .sidebar-content {
    padding: var(--spacing-sm);
  }

  .sidebar h3,
  .sidebar h4,
  .filter-label span,
  .legend,
  .stats {
    display: none;
  }

  .filter-label {
    justify-content: center;
    padding: var(--spacing-sm);
  }

  .checkmark {
    margin: 0;
  }
}

@media (max-width: 768px) {
  .app-container {
    grid-template-areas:
      "header"
      "main";
    grid-template-columns: 1fr;
    grid-template-rows: var(--header-height) 1fr;
  }

  .sidebar {
    display: none;
  }

  .header-content {
    padding: 0 var(--spacing-md);
  }

  .logo h1 {
    display: none;
  }

  .entity {
    min-width: 180px;
    max-width: 220px;
  }

  .modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--spacing-md);
  }

  .property-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .relationship-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .header-controls {
    gap: var(--spacing-sm);
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }

  .btn span {
    display: none;
  }

  .entity {
    min-width: 160px;
    max-width: 200px;
  }

  .entity-title {
    font-size: var(--font-size-base);
  }

  .toast {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    transform: translateY(-100%);
  }

  .toast.active {
    transform: translateY(0);
  }
}

/* Print Styles */
@media print {
  .header,
  .sidebar,
  .modal,
  .loading-overlay,
  .toast {
    display: none !important;
  }

  .app-container {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  .main-content {
    background: var(--white);
  }

  .canvas-container {
    background: none;
  }

  .entity {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }

  .relationship-line {
    stroke: var(--gray-800);
    stroke-width: 1;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --white: #1e293b;
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }

  body {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
  }
}

/* Focus Styles for Accessibility */
.btn:focus,
.filter-label:focus-within,
.view-details-btn:focus,
.modal-close:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.entity:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 4px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .entity {
    border: 2px solid var(--gray-900);
  }

  .relationship-line {
    stroke: var(--gray-900);
    stroke-width: 3;
  }

  .btn {
    border: 2px solid currentColor;
  }
}
