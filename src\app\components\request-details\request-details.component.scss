// Absa Brand Colors
:root {
  --absa-red: #CE0E2D;
  --absa-dark-blue: #003366;
  --absa-light-blue: #0066CC;
  --absa-gold: #FFB81C;
  --absa-green: #00A651;
  --absa-white: #FFFFFF;
  --absa-gray-light: #F5F5F5;
  --absa-gray-medium: #6C757D;
  --absa-gray-dark: #495057;
}

.request-details-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

// Main content area - Compact layout with beautiful styling
.details-content {
  padding: 1.25rem 1.75rem;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 64px); // Account for toolbar height
  display: flex;
  flex-direction: column;
  gap: 1.25rem; // Reduced spacing between sections

  // Large screens - utilize more space with proper margins
  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 1.5rem 2rem;
    gap: 1.5rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1400px;
    padding: 1.25rem 1.75rem;
    gap: 1.25rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    padding: 1rem 1.5rem;
    gap: 1rem;
  }

  @media (max-width: 899px) {
    padding: 1rem 1.25rem;
    gap: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
    gap: 0.75rem;
  }
}

// Compact Header - Reduced size with beautiful styling
.compact-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--absa-white);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.08);
  border-left: 4px solid var(--absa-red);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;

    .page-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--absa-dark-blue);
      margin: 0;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }

    .request-id {
      font-size: 0.85rem;
      color: var(--absa-dark-blue);
      font-family: 'Courier New', monospace;
      background: rgba(0,102,204,0.1);
      padding: 0.3rem 0.8rem;
      border-radius: 8px;
      font-weight: 600;
      border: 1px solid rgba(0,102,204,0.2);
      letter-spacing: 0.5px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 1rem;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }

    .status-chip {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      color: var(--absa-white);

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
      }

      &.status-pending {
        background: var(--absa-gold);
      }

      &.status-approved {
        background: var(--absa-green);
      }

      &.status-issued {
        background: var(--absa-light-blue);
      }

      &.status-returned {
        background: var(--absa-gray-medium);
      }

      &.status-completed {
        background: var(--absa-green);
      }

      &.status-rejected, &.status-cancelled {
        background: var(--absa-red);
      }
    }

    .priority-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: #ff4500;
      border-radius: 20px;
      color: var(--absa-white);
      font-weight: 600;
      font-size: 0.8rem;
      animation: pulse 2s infinite;

      .urgent-icon {
        font-size: 1rem;
      }
    }
  }
}

// Enhanced Timeline with Perfect Icon Centering - Compact Version
.compact-timeline {
  background: var(--absa-white);
  border-radius: 12px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.08);
  padding: 1.25rem 1.5rem;
  border-left: 4px solid var(--absa-light-blue);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  }

  .timeline-title {
    color: var(--absa-dark-blue);
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 1.25rem 0;
    text-align: center;
    letter-spacing: 0.5px;
  }

  .timeline-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding: 0 1rem;

    @media (max-width: 768px) {
      flex-wrap: wrap;
      justify-content: center;
      gap: 1.5rem;
      padding: 0;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
      opacity: 0.4;
      transition: all 0.4s ease;
      flex: 1;
      max-width: 100px;
      position: relative;

      &.active, &.completed {
        opacity: 1;
      }

      &.active {
        transform: scale(1.08);
      }

      .step-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: var(--absa-gray-light);
        border: 2px solid var(--absa-gray-medium);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s ease;
        position: relative;

        // Perfect icon centering
        mat-icon {
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;
          color: var(--absa-gray-medium);
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      &.completed .step-icon {
        background: var(--absa-green);
        border-color: var(--absa-green);
        box-shadow: 0 4px 12px rgba(0,166,81,0.3);

        mat-icon {
          color: var(--absa-white);
        }
      }

      &.active .step-icon {
        background: var(--absa-light-blue);
        border-color: var(--absa-light-blue);
        box-shadow: 0 6px 20px rgba(0,102,204,0.4);

        mat-icon {
          color: var(--absa-white);
        }
      }

      .step-text {
        font-size: 0.85rem;
        font-weight: 600;
        text-align: center;
        color: var(--absa-dark-blue);
        white-space: nowrap;
        letter-spacing: 0.3px;

        @media (max-width: 768px) {
          font-size: 0.8rem;
        }
      }
    }

    .step-line {
      flex: 1;
      height: 3px;
      background: var(--absa-gray-light);
      margin: 0 0.5rem;
      border-radius: 2px;
      transition: all 0.4s ease;
      position: relative;
      top: -12px; // Align with step icons

      &.completed {
        background: linear-gradient(90deg, var(--absa-green) 0%, var(--absa-light-blue) 100%);
        box-shadow: 0 2px 8px rgba(0,166,81,0.2);
      }

      @media (max-width: 768px) {
        display: none;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1.25rem;
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.app-toolbar {
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;
    margin-left: 0.5rem;

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1); // Make logo white on red background
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    margin-left: 16px;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }
}

// Enhanced accessibility and responsive design
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }
  }

  .priority-indicator {
    animation: none !important;
  }

  .timeline-step {
    &.active {
      transform: none !important;
    }
  }
}

// Mobile-first responsive design
@media (max-width: 480px) {
  .request-header {
    padding: 1rem;
    margin-bottom: 1rem;

    .request-info {
      .request-title {
        font-size: 1.5rem;
      }

      .request-id {
        font-size: 0.9rem;
      }
    }
  }

  .progress-timeline-section {
    .timeline-card {
      padding: 1rem;

      .timeline-container {
        .timeline-step {
          .step-circle {
            width: 40px;
            height: 40px;

            mat-icon {
              font-size: 1.2rem;
            }
          }

          .step-label {
            font-size: 0.7rem;
          }
        }
      }
    }
  }

  .details-content {
    padding: 0.75rem;
  }

  .modern-card {
    .card-header {
      padding: 1rem 1.5rem;
      text-align: center;

      .header-content {
        .card-title {
          font-size: 1.2rem;
        }

        .card-subtitle {
          font-size: 0.9rem;
        }
      }
    }

    .card-content {
      padding: 1rem;
    }
  }

  .main-info-card {
    .info-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .info-item {
      text-align: center;
    }
  }

  .simple-notes-list {
    .simple-note-item {
      grid-template-columns: 1fr;
      gap: 0.5rem;
      text-align: center;

      .note-quantity,
      .note-total {
        text-align: center;
      }
    }
  }

  .simple-extras {
    .extras-items {
      justify-content: center;

      .extra-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .modern-card {
    border-width: 3px;
    border-color: var(--absa-dark-blue);

    .card-header {
      background: var(--absa-dark-blue);
    }

    &.requester-card .card-header {
      background: var(--absa-green);
    }

    &.status-card .card-header {
      background: var(--absa-red);
    }

    &.processing-card .card-header {
      background: var(--absa-gold);
    }

    &.notes-card .card-header {
      background: var(--absa-dark-blue);
    }
  }

  .status-chip {
    border-width: 3px;
  }
}

// Print styles
@media print {
  .app-toolbar {
    display: none;
  }

  .request-header {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    border: 2px solid #ccc !important;

    .request-info {
      .request-title,
      .request-id {
        color: black !important;
      }

      .status-chip {
        background: #f0f0f0 !important;
        color: black !important;
        border: 2px solid #ccc !important;
      }
    }
  }

  .progress-timeline-section {
    display: none;
  }

  .modern-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 2px solid #ccc !important;

    .card-header {
      background: #f0f0f0 !important;
      color: black !important;

      .header-icon {
        background: #e0e0e0 !important;

        mat-icon {
          color: black !important;
        }
      }
    }
  }
}



// Modern Card Styles
.modern-card {
  background: linear-gradient(135deg, var(--absa-white) 0%, #F8F9FA 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,102,204,0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.12);
    border-color: rgba(0,102,204,0.2);
  }

  .card-header {
    background: linear-gradient(135deg, var(--absa-light-blue) 0%, var(--absa-dark-blue) 100%);
    color: var(--absa-white);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;

    .header-icon {
      background: rgba(255,255,255,0.1);
      padding: 0.75rem;
      border-radius: 12px;
      backdrop-filter: blur(10px);

      mat-icon {
        font-size: 1.5rem;
        color: var(--absa-white);
      }
    }

    .header-content {
      flex: 1;

      .card-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        color: var(--absa-white) !important;
      }

      .card-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0.25rem 0 0 0;
        font-weight: 400;
        color: var(--absa-white) !important;
      }
    }
  }

  .card-content {
    padding: 2rem;

    @media (max-width: 768px) {
      padding: 1.5rem;
    }
  }

  // Specific card variants
  &.requester-card .card-header {
    background: linear-gradient(135deg, var(--absa-green) 0%, #00A651 100%);
  }

  &.status-card .card-header {
    background: linear-gradient(135deg, var(--absa-red) 0%, #E31837 100%);
  }

  &.processing-card .card-header {
    background: linear-gradient(135deg, var(--absa-gold) 0%, #FFB81C 100%);
    color: var(--absa-dark-blue);

    .header-icon mat-icon {
      color: var(--absa-dark-blue);
    }

    .card-title, .card-subtitle {
      color: var(--absa-dark-blue);
    }
  }

  &.notes-card .card-header {
    background: linear-gradient(135deg, var(--absa-dark-blue) 0%, #003366 100%);

    .header-content {
      .card-title,
      .card-subtitle {
        color: var(--absa-white) !important;
      }
    }
  }
}

// Enhanced Compact Overview Grid with Better Spacing
.compact-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;

  @media (min-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.75rem;
  }

  @media (max-width: 899px) {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  @media (max-width: 480px) {
    gap: 1rem;
  }
}

// Enhanced Compact Cards with Better Visual Hierarchy
.compact-card {
  background: var(--absa-white);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  padding: 1.75rem;
  transition: all 0.3s ease;
  border-left: 5px solid transparent;
  position: relative;
  overflow: hidden;

  // Subtle background pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(0,102,204,0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
  }

  &.info-card {
    border-left-color: var(--absa-light-blue);

    .header-icon {
      background: rgba(0,102,204,0.1);
      color: var(--absa-light-blue);
    }
  }

  &.status-card {
    border-left-color: var(--absa-green);

    .header-icon {
      background: rgba(0,166,81,0.1);
      color: var(--absa-green);
    }
  }

  &.amount-card {
    border-left-color: var(--absa-gold);

    .header-icon {
      background: rgba(255,184,28,0.1);
      color: var(--absa-gold);
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.25rem;
    position: relative;
    z-index: 1;

    .header-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .header-text {
      flex: 1;

      h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--absa-dark-blue);
        margin: 0;
        line-height: 1.3;
        letter-spacing: 0.3px;
      }

      p {
        font-size: 0.9rem;
        color: var(--absa-gray-medium);
        margin: 0.5rem 0 0 0;
        line-height: 1.4;
        font-weight: 500;
      }
    }
  }

  .card-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: relative;
    z-index: 1;

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(0,0,0,0.05);

      &:last-child {
        border-bottom: none;
      }

      .stat-label {
        font-size: 0.85rem;
        color: var(--absa-gray-medium);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-value {
        font-size: 1rem;
        color: var(--absa-dark-blue);
        font-weight: 700;
        text-align: right;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1.5rem;

    .card-header {
      gap: 0.75rem;
      margin-bottom: 1rem;

      .header-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
      }

      .header-text h3 {
        font-size: 1.1rem;
      }
    }
  }
}

// Simplified Info Cards Grid
.info-cards-grid {
  margin-bottom: 1rem;
}

// Main Info Card Styling
.main-info-card {
  .card-header {
    background: linear-gradient(135deg, var(--absa-light-blue) 0%, var(--absa-dark-blue) 100%);

    .header-content {
      .card-title,
      .card-subtitle {
        color: var(--absa-white) !important;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .info-label {
      font-size: 0.9rem;
      color: var(--absa-gray-medium);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .info-value {
      font-size: 1rem;
      color: var(--absa-dark-blue);
      font-weight: 600;
    }
  }

  .deadline-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 69, 0, 0.1);
    border: 2px solid #ff4500;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1.5rem;
    color: #ff4500;
    font-weight: 600;

    .alert-icon {
      font-size: 1.5rem;
      animation: pulse 2s infinite;
    }

    .alert-text {
      flex: 1;
    }
  }
}

// Info Row Styles
.info-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0,0,0,0.05);

  &:last-child {
    border-bottom: none;
  }

  .info-icon {
    font-size: 1.5rem;
    color: var(--absa-light-blue);
    flex-shrink: 0;
    width: 2rem;
    text-align: center;

    &.warning {
      color: var(--absa-gold);
      animation: pulse 2s infinite;
    }
  }

  .info-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    .info-label {
      font-size: 0.9rem;
      color: var(--absa-gray-medium);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .info-value {
      font-size: 1.1rem;
      color: var(--absa-dark-blue);
      font-weight: 600;

      &.overdue {
        color: var(--absa-red);
        font-weight: 700;
      }

      &.positive {
        color: var(--absa-green);
      }

      &.negative {
        color: var(--absa-red);
      }
    }
  }
}

// Status Display
.status-display {
  text-align: center;
  margin-bottom: 1.5rem;

  .status-chip-large {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    transition: all 0.3s ease;

    mat-icon {
      font-size: 1.5rem;
    }

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 24px rgba(0,0,0,0.15);
    }

    &.status-pending {
      background: linear-gradient(135deg, var(--absa-gold) 0%, #FFB81C 100%);
      color: var(--absa-dark-blue);
    }

    &.status-approved {
      background: linear-gradient(135deg, var(--absa-green) 0%, #00A651 100%);
      color: var(--absa-white);
    }

    &.status-issued {
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #0066CC 100%);
      color: var(--absa-white);
    }

    &.status-returned {
      background: linear-gradient(135deg, var(--absa-gray-medium) 0%, #6C757D 100%);
      color: var(--absa-white);
    }

    &.status-completed {
      background: linear-gradient(135deg, var(--absa-green) 0%, #00A651 100%);
      color: var(--absa-white);
    }

    &.status-rejected, &.status-cancelled {
      background: linear-gradient(135deg, var(--absa-red) 0%, #CE0E2D 100%);
      color: var(--absa-white);
    }
  }
}

// Enhanced Cash Breakdown Container with Vibrant Colors - Compact
.compact-notes-container {
  background: linear-gradient(135deg, var(--absa-white) 0%, #f8fffe 100%);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,102,204,0.12);
  padding: 1.5rem;
  border: 1px solid rgba(0,102,204,0.1);
  border-left: 4px solid var(--absa-light-blue);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;

  // Enhanced background pattern with color
  &::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(0,102,204,0.06) 0%, rgba(0,166,81,0.03) 50%, transparent 70%);
    pointer-events: none;
    border-radius: 50%;
  }

  &:hover {
    box-shadow: 0 8px 30px rgba(0,102,204,0.18);
    transform: translateY(-2px);
    border-left-color: var(--absa-green);
  }

  .notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, rgba(0,102,204,0.08) 0%, rgba(0,166,81,0.05) 100%);
    border-radius: 10px;
    border: 1px solid rgba(0,102,204,0.15);
    position: relative;
    z-index: 1;

    h3 {
      font-size: 1.2rem;
      font-weight: 800;
      background: linear-gradient(135deg, var(--absa-dark-blue) 0%, var(--absa-light-blue) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
      letter-spacing: 0.5px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .notes-summary {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 0.4rem;
      background: var(--absa-white);
      padding: 0.6rem 0.8rem;
      border-radius: 6px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.1);

      .total-amount {
        font-size: 1.1rem;
        font-weight: 900;
        background: linear-gradient(135deg, var(--absa-green) 0%, #00d084 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: 0.3px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
      }

      .notes-count {
        font-size: 0.7rem;
        color: var(--absa-light-blue);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        background: rgba(0,102,204,0.1);
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
      }
    }
  }

  .notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;

    @media (min-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .note-card {
      background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,255,254,0.9) 100%);
      border: 2px solid rgba(0,102,204,0.12);
      border-radius: 12px;
      padding: 1rem;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, var(--absa-light-blue) 0%, var(--absa-green) 50%, var(--absa-gold) 100%);
      }

      &::after {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        background: radial-gradient(circle, rgba(0,102,204,0.1) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(240,255,252,1) 100%);
        border-color: rgba(0,102,204,0.25);
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,102,204,0.15);
      }

      .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .denomination {
          font-size: 1.2rem;
          font-weight: 800;
          color: var(--absa-dark-blue);
          letter-spacing: 0.3px;
        }

        .series {
          font-size: 0.75rem;
          color: var(--absa-gray-medium);
          background: rgba(0,51,102,0.1);
          padding: 0.3rem 0.6rem;
          border-radius: 6px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .note-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.75rem;
        border-top: 1px solid rgba(0,102,204,0.1);

        .quantity {
          font-size: 0.9rem;
          color: var(--absa-gray-medium);
          font-weight: 600;
        }

        .subtotal {
          font-size: 1.1rem;
          font-weight: 700;
          color: var(--absa-green);
          letter-spacing: 0.3px;
        }
      }
    }
  }

  .coins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;

    @media (min-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .coin-card {
      background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,251,235,0.9) 100%);
      border: 2px solid rgba(255,184,28,0.2);
      border-radius: 12px;
      padding: 1rem;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, var(--absa-gold) 0%, var(--absa-light-blue) 50%, var(--absa-green) 100%);
      }

      &::after {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        background: radial-gradient(circle, rgba(255,184,28,0.1) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(255,248,220,1) 100%);
        border-color: rgba(255,184,28,0.4);
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(255,184,28,0.2);
      }

      .coin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .denomination {
          font-size: 1.2rem;
          font-weight: 800;
          color: var(--absa-dark-blue);
          letter-spacing: 0.3px;
        }

        .coin-type {
          font-size: 0.75rem;
          color: var(--absa-gold);
          background: rgba(255,184,28,0.15);
          padding: 0.3rem 0.6rem;
          border-radius: 6px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .coin-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.75rem;
        border-top: 1px solid rgba(255,184,28,0.2);

        .quantity {
          font-size: 0.9rem;
          color: var(--absa-gray-medium);
          font-weight: 600;
        }

        .subtotal {
          font-size: 1.1rem;
          font-weight: 700;
          color: var(--absa-green);
          letter-spacing: 0.3px;
        }
      }
    }
  }

  .extras-section {
    border-top: 2px solid rgba(0,51,102,0.1);
    padding-top: 1.25rem;
    position: relative;
    z-index: 1;

    .extras-header {
      font-size: 1rem;
      font-weight: 700;
      color: var(--absa-dark-blue);
      margin-bottom: 1rem;
      letter-spacing: 0.3px;
    }

    .extras-list {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;

      .extra-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(255,184,28,0.1);
        color: var(--absa-gold);
        padding: 0.75rem 1.25rem;
        border-radius: 10px;
        font-size: 0.9rem;
        font-weight: 600;
        border: 2px solid rgba(255,184,28,0.2);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255,184,28,0.15);
          border-color: rgba(255,184,28,0.3);
          transform: translateY(-1px);
        }

        mat-icon {
          font-size: 1.1rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1.25rem;
  }
}

// Section spacing - eliminate excessive bottom white space
.info-section,
.notes-section,
.comments-section,
.actions-section {
  // No margin needed - parent container handles spacing with gap
  margin-bottom: 0;
}

// Ensure the last section doesn't add unnecessary space
.details-content > *:last-child {
  margin-bottom: 0;
}

// Enhanced Action Cards Styling - Compact
.actions-section {
  .action-card {
    background: linear-gradient(135deg, var(--absa-white) 0%, #f8fffe 100%);
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,102,204,0.1);
    border-left: 4px solid var(--absa-light-blue);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;

    // Background pattern
    &::before {
      content: '';
      position: absolute;
      top: -30px;
      right: -30px;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle, rgba(0,102,204,0.05) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(0,102,204,0.15);
      border-left-color: var(--absa-green);
    }

    &.danger {
      border-left-color: var(--absa-red);

      &::before {
        background: radial-gradient(circle, rgba(206,14,45,0.05) 0%, transparent 70%);
      }

      &:hover {
        border-left-color: #d32f2f;
        box-shadow: 0 10px 30px rgba(206,14,45,0.15);
      }
    }

    mat-card-header {
      background: linear-gradient(135deg, rgba(0,102,204,0.06) 0%, rgba(0,166,81,0.03) 100%);
      margin: -24px -24px 16px -24px;
      padding: 1.25rem 2rem;
      border-radius: 16px 16px 0 0;
      position: relative;
      z-index: 1;

      mat-card-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--absa-dark-blue);
        margin: 0;
        letter-spacing: 0.3px;
        text-align: left;
      }

      mat-card-subtitle {
        font-size: 0.85rem;
        color: var(--absa-gray-medium);
        margin-top: 0.5rem;
        font-weight: 500;
        line-height: 1.4;
        text-align: left;
      }
    }

    mat-card-content {
      padding: 0 2rem;
      position: relative;
      z-index: 1;

      .mat-mdc-form-field {
        width: 100%;
        margin-bottom: 0.75rem;

        .mat-mdc-text-field-wrapper {
          background: rgba(255,255,255,0.8);
          border-radius: 10px;
        }
      }

      .deadline-info {
        display: flex;
        align-items: center;
        gap: 0.6rem;
        background: rgba(0,102,204,0.08);
        padding: 0.75rem 1rem;
        border-radius: 10px;
        margin-top: 0.75rem;
        border: 1px solid rgba(0,102,204,0.15);

        mat-icon {
          color: var(--absa-light-blue);
          font-size: 1.1rem;
        }

        span {
          color: var(--absa-dark-blue);
          font-weight: 600;
          font-size: 0.8rem;
        }
      }
    }

    mat-card-actions {
      padding: 1rem 2rem 1.25rem 2rem;
      position: relative;
      z-index: 1;

      button {
        min-width: 140px;
        height: 40px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        mat-icon {
          margin-right: 0.4rem;
          font-size: 1rem;
        }
      }
    }
  }
}

.info-card,
.notes-card,
.comments-card,
.action-card {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 12px;

  mat-card-header {
    mat-card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 0.5rem;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
  }

  span {
    color: #555;
    font-size: 1rem;
  }

  .status-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    align-self: flex-start;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    color: white !important;

    mat-icon {
      font-size: 1.125rem;
      width: 1.125rem;
      height: 1.125rem;
      color: white !important;
    }

    span {
      line-height: 1;
      color: white !important;
    }

    &.status-pending {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border: 2px solid #ff9800;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-approved {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      border: 2px solid #2196f3;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-issued {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
      border: 2px solid #9c27b0;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-returned {
      background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
      border: 2px solid #00bcd4;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-completed {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      border: 2px solid #4caf50;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-cancelled {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      border: 2px solid #f44336;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-rejected {
      background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
      border: 2px solid #e91e63;

      mat-icon, span {
        color: white !important;
      }
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
  }

  mat-chip {
    align-self: flex-start;

    mat-icon {
      margin-right: 0.25rem;
      font-size: 1rem;
    }
  }
}

// Simplified Notes Section Styles
.simple-notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .simple-note-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    align-items: center;
    padding: 1rem;
    background: rgba(0,102,204,0.05);
    border-radius: 12px;
    border: 1px solid rgba(0,102,204,0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0,102,204,0.08);
      border-color: rgba(0,102,204,0.2);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 0.5rem;
      text-align: center;
    }

    .note-denomination {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .denomination-text {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--absa-dark-blue);
      }

      .series-text {
        font-size: 0.8rem;
        color: var(--absa-gray-medium);
        font-weight: 500;
      }
    }

    .note-quantity {
      font-size: 1rem;
      color: var(--absa-gray-dark);
      font-weight: 500;
      text-align: center;

      @media (max-width: 768px) {
        text-align: center;
      }
    }

    .note-total {
      font-size: 1.1rem;
      font-weight: 700;
      color: var(--absa-red);
      text-align: right;

      @media (max-width: 768px) {
        text-align: center;
      }
    }
  }
}

// Simple Extras Styling
.simple-extras {
  margin-top: 1.5rem;

  .extras-divider {
    height: 1px;
    background: rgba(0,102,204,0.2);
    margin-bottom: 1rem;
  }

  .extras-items {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;

    .extra-tag {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(255,184,28,0.1);
      color: var(--absa-gold);
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.9rem;
      border: 1px solid rgba(255,184,28,0.3);

      mat-icon {
        font-size: 1rem;
      }
    }
  }
}

// Simplified layout - removed complex additional items and total summary sections

.note-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  .note-denomination {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1976d2;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .note-quantity {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
  }

  .note-total {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
  }

  &.coins-item {
    background-color: #fff3e0;
    border: 2px solid #ff9800;

    .note-denomination {
      color: #ff9800;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    .note-quantity {
      color: #ff9800;
      font-style: italic;
    }

    .note-total {
      color: #4caf50;
      font-size: 1.5rem;
      font-weight: 700;
    }
  }

  &.dye-pack-item {
    background-color: #fff3e0;
    border: 2px solid #e31837;

    .note-denomination {
      color: #e31837;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    .note-quantity {
      color: #e31837;
      font-style: italic;
    }

    .note-total {
      color: #4caf50;
      font-size: 1.5rem;
      font-weight: 700;
    }
  }
}

.total-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border-radius: 8px;
  margin-top: 1rem;

  .total-label {
    font-size: 1.1rem;
    font-weight: 500;
  }

  .total-value {
    font-size: 1.5rem;
    font-weight: 700;
  }

  .coins-indicator {
    font-size: 0.9rem;
    font-style: italic;
    opacity: 0.9;
    margin-left: 8px;
  }

  .dye-pack-indicator {
    font-size: 0.9rem;
    font-style: italic;
    opacity: 0.9;
    margin-left: 8px;
    color: #e31837;
  }
}

.comments-card {
  mat-card-content {
    p {
      font-size: 1rem;
      line-height: 1.6;
      color: #555;
      margin: 0;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #1976d2;
    }
  }
}

.action-card {
  margin-bottom: 1.5rem;

  &.danger {
    border-left: 4px solid #f44336;
  }

  mat-card-content {
    .full-width {
      width: 100%;
      margin-bottom: 1rem;
    }
  }

  mat-card-actions {
    padding: 1rem 1.5rem;

    button {
      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Form field customizations
mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 0.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .info-grid {
    gap: 0.75rem;
  }

  .info-item {
    padding: 0.75rem;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
  }

  .notes-grid {
    gap: 0.75rem;
  }

  .note-item {
    padding: 0.75rem;
  }

  .total-amount {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;

    .total-value {
      font-size: 1.25rem;
    }
  }

  .action-card {
    mat-card-content {
      padding: 1rem;
    }

    mat-card-actions {
      padding: 1rem;

      button {
        width: 100%;
      }
    }
  }
}

// Loading state
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;

  mat-spinner {
    margin: 0 auto;
  }
}

// Error state
.error-container {
  text-align: center;
  padding: 3rem;
  color: #666;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    color: #f44336;
  }

  h2 {
    margin-bottom: 1rem;
    color: #333;
  }

  p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

// 3PM Deadline Styles
.deadline-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background-color: #e3f2fd;
  border-radius: 4px;
  border-left: 4px solid #2196f3;

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-weight: 500;
    color: #1976d2;
  }
}

.deadline-status {
  .overdue-text {
    color: #d32f2f;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .deadline-text {
    color: #1976d2;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.mat-form-field {
  &.ng-invalid {
    .mat-form-field-outline-thick {
      color: #f44336;
    }
  }
}



// Clickable Logo Styles
.clickable-logo {
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;

  &:hover {
    opacity: 0.8;
  }

  &:active {
    opacity: 0.6;
  }
}

// Rejection Details Styles
.rejection-section {
  margin-bottom: 2rem;

  .rejection-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 4px solid #f44336;

    &.auto-rejected {
      border-left-color: #ff5722;
      background-color: #fff3e0;
    }

    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: #f44336;
      }

      mat-card-subtitle {
        color: #666;
        margin-top: 0.5rem;
      }
    }

    .rejection-details {
      .rejection-info {
        margin-bottom: 1.5rem;

        .info-item {
          display: flex;
          margin-bottom: 0.75rem;
          align-items: flex-start;

          .label {
            font-weight: 600;
            color: #333;
            min-width: 140px;
            margin-right: 1rem;
          }

          .value {
            color: #666;
            flex: 1;
            word-break: break-word;
          }
        }
      }

      .rejection-actions {
        padding: 1rem;
        background-color: #f5f5f5;
        border-radius: 8px;
        border-left: 4px solid #ff9800;

        h4 {
          margin: 0 0 1rem 0;
          color: #ff9800;
          font-size: 1rem;
          font-weight: 600;
        }

        .suggestions-list {
          margin: 0;
          padding-left: 1.5rem;

          li {
            margin-bottom: 0.5rem;
            line-height: 1.4;
            color: #555;
          }
        }
      }
    }
  }
}
