.manager-dashboard-container {
  min-height: calc(100vh - 64px); // Subtract header height
  background-color: #f5f5f5;
}

.app-toolbar {
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1); // Make logo white on red background
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    margin-left: 16px;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .logout-button {
    margin-left: 8px;
    color: white;

    mat-icon {
      margin-right: 4px;
    }
  }
}

// Enhanced accessibility
@media (prefers-reduced-motion: reduce) {
  .toolbar-absa-logo {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }
  }
}

.dashboard-content {
  padding: 1rem 2rem 0.5rem 2rem; // Reduced bottom padding
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: calc(100vh - 64px);

  // Large screens - utilize more space
  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 1rem 3rem 0.5rem 3rem; // Reduced bottom padding
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1600px;
    padding: 1rem 2.5rem 0.5rem 2.5rem; // Reduced bottom padding
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    max-width: 1200px;
    padding: 1rem 2rem 0.5rem 2rem; // Reduced bottom padding
  }

  @media (max-width: 768px) {
    padding: 1rem 1rem 0.5rem 1rem; // Reduced bottom padding
  }
}

// Modern Welcome Header
.welcome-header {
  background: linear-gradient(135deg, var(--absa-red) 0%, #d32f2f 100%);
  border-radius: 16px;
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(206,14,45,0.2);
  color: white;

  .welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  .user-info {
    .welcome-title {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: white;
      letter-spacing: 0.5px;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .user-role {
      font-size: 1rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 500;
    }
  }

  .quick-stats {
    display: flex;
    gap: 2rem;

    @media (max-width: 768px) {
      gap: 1.5rem;
    }

    .quick-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
}

// Modern Statistics Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 5 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  // Medium screens - 2 columns
  @media (min-width: 600px) and (max-width: 899px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  // Small screens - 1 column but allow 2 if space permits
  @media (min-width: 480px) and (max-width: 599px) {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }

  // Very small screens - single column
  @media (max-width: 479px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

// Modern Stat Cards
.modern-stat-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);
  backdrop-filter: blur(10px);

  // Responsive padding with better proportions
  @media (max-width: 768px) {
    padding: 1.25rem;
    border-radius: 14px;
    box-shadow: 0 3px 16px rgba(0,0,0,0.06);
  }

  @media (max-width: 599px) {
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.05);
  }

  @media (max-width: 479px) {
    padding: 0.875rem;
    border-radius: 10px;
  }

  @media (max-width: 360px) {
    padding: 0.75rem;
    border-radius: 8px;
  }

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
      border-color: rgba(0,102,204,0.1);

      .stat-number {
        color: var(--absa-light-blue);
      }
    }

    &:active {
      transform: translateY(-2px);
      transition-duration: 0.1s;
    }
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    opacity: 0.1;
    transform: translate(30px, -30px);
  }

  .card-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 1rem;

    // Responsive gap sizing
    @media (max-width: 599px) {
      gap: 0.75rem;
    }

    @media (max-width: 479px) {
      gap: 0.625rem;
    }
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;

    // Responsive icon sizing
    @media (max-width: 768px) {
      width: 50px;
      height: 50px;
      border-radius: 10px;
    }

    @media (max-width: 599px) {
      width: 45px;
      height: 45px;
      border-radius: 8px;
    }

    @media (max-width: 479px) {
      width: 40px;
      height: 40px;
    }

    mat-icon {
      font-size: 1.75rem;
      width: 1.75rem;
      height: 1.75rem;
      color: white;

      // Responsive icon sizing
      @media (max-width: 768px) {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      @media (max-width: 599px) {
        font-size: 1.35rem;
        width: 1.35rem;
        height: 1.35rem;
      }

      @media (max-width: 479px) {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }

    .alert-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 18px;
      height: 18px;
      background-color: #CC0000;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 700;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
  }

  .stat-details {
    flex: 1;
    min-width: 0; // Allow text to shrink

    .stat-number {
      font-size: 1.75rem;
      font-weight: 700;
      line-height: 1.1;
      margin-bottom: 0.25rem;
      color: var(--absa-dark-blue);
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      letter-spacing: -0.02em;
      transition: color 0.3s ease, transform 0.2s ease;
      font-variant-numeric: tabular-nums;

      // Responsive font sizing with better scaling
      @media (max-width: 768px) {
        font-size: 1.4rem;
        line-height: 1.15;
      }

      @media (max-width: 599px) {
        font-size: 1.2rem;
        line-height: 1.2;
        letter-spacing: -0.01em;
      }

      @media (max-width: 479px) {
        font-size: 1.1rem;
        line-height: 1.25;
      }

      @media (max-width: 360px) {
        font-size: 1rem;
        line-height: 1.3;
        font-weight: 600;
      }

      // Special handling for very long numbers
      @media (max-width: 320px) {
        font-size: 0.9rem;
        transform: scale(0.95);
        transform-origin: left center;
      }
    }

    .stat-label {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--absa-dark-blue);
      margin-bottom: 0.2rem;
      line-height: 1.3;
      letter-spacing: 0.01em;

      // Responsive font sizing
      @media (max-width: 768px) {
        font-size: 0.85rem;
        margin-bottom: 0.15rem;
      }

      @media (max-width: 599px) {
        font-size: 0.8rem;
        line-height: 1.25;
      }

      @media (max-width: 479px) {
        font-size: 0.75rem;
        font-weight: 500;
      }

      @media (max-width: 360px) {
        font-size: 0.7rem;
      }
    }

    .stat-description {
      font-size: 0.8rem;
      color: var(--absa-gray-medium);
      font-weight: 500;
      line-height: 1.3;
      opacity: 0.9;

      // Responsive font sizing
      @media (max-width: 768px) {
        font-size: 0.75rem;
      }

      @media (max-width: 599px) {
        font-size: 0.7rem;
        line-height: 1.25;
      }

      @media (max-width: 479px) {
        font-size: 0.65rem;
        opacity: 0.8;
      }

      @media (max-width: 360px) {
        font-size: 0.6rem;
      }
    }
  }

  // Card-specific styling
  &.inventory-card {
    .card-background {
      background: var(--absa-green);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-green) 0%, #388e3c 100%);
    }
  }

  &.active-card {
    .card-background {
      background: var(--absa-light-blue);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
    }
  }

  &.alerts-card {
    .card-background {
      background: var(--absa-gold);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-gold) 0%, #f57c00 100%);
    }
  }

  &.warning-card {
    .card-background {
      background: #ff9800;
    }
    .stat-icon {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: var(--absa-light-blue);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
    }
  }
}

// Actions Section
.actions-section {
  margin-bottom: 2rem;

  .actions-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

    mat-card-header {
      background-color: var(--absa-white);
      border-bottom: 2px solid var(--absa-red);

      mat-card-title {
        color: var(--absa-dark-blue);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1rem;
      margin-top: 1rem;

      // Large screens - 4 columns (symmetrical)
      @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
      }

      // Medium screens - 2 columns (symmetrical)
      @media (min-width: 600px) and (max-width: 1199px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
      }

      // Mobile screens - 1 column
      @media (max-width: 599px) {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .action-button {
        padding: 1rem;
        height: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border-radius: 8px;
        font-weight: 500;
        min-height: 80px;
        transition: all 0.2s ease;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        &:active {
          transform: translateY(0);
        }

        // Mobile adjustments
        @media (max-width: 599px) {
          padding: 0.875rem;
          font-size: 0.9rem;
          min-height: 70px;

          mat-icon {
            font-size: 1.3rem;
            width: 1.3rem;
            height: 1.3rem;
          }
        }

        // Very small screens
        @media (max-width: 400px) {
          padding: 0.75rem;
          font-size: 0.85rem;
          min-height: 60px;

          mat-icon {
            font-size: 1.2rem;
            width: 1.2rem;
            height: 1.2rem;
          }
        }
      }
    }
  }
}

// Modern Requests Section
.requests-section {
  .requests-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
  }

  .section-header {
    background: linear-gradient(135deg, var(--absa-dark-blue) 0%, var(--absa-light-blue) 100%);
    color: white;
    padding: 1.5rem 2rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      letter-spacing: 0.5px;
      color: white !important;
    }

    .section-subtitle {
      font-size: 1rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 500;
      color: white !important;
    }
  }

  .requests-content {
    padding: 0;

    ::ng-deep .mat-mdc-tab-group {
      .mat-mdc-tab-header {
        border-bottom: 1px solid rgba(0,0,0,0.1);
        background: #f8f9fa;

        .mat-mdc-tab {
          min-width: 160px;
          font-weight: 600;
          color: var(--absa-dark-blue);

          &.mdc-tab--active {
            color: var(--absa-red);
          }
        }

        .mat-ink-bar {
          background-color: var(--absa-red);
          height: 3px;
        }
      }

      .mat-mdc-tab-body-wrapper {
        .mat-mdc-tab-body {
          .mat-mdc-tab-body-content {
            padding: 0;
          }
        }
      }
    }
  }
}

.tab-content {
  padding: 2rem;
  background: white;
}

.table-container {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid rgba(0,0,0,0.05);
}

.requests-table {
  width: 100%;
  background: white;

  th {
    font-weight: 700;
    color: var(--absa-dark-blue);
    background: #f8f9fa;
    padding: 1.25rem 1rem;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(0,0,0,0.1);
  }

  td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: var(--absa-gray-dark);
    font-size: 0.9rem;
  }

  tr:hover {
    background-color: rgba(206,14,45,0.02);
  }
}

// Inventory Overview
.inventory-overview {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .inventory-summary-card {
    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1rem;

      // Large screens - 3 columns
      @media (min-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }

      // Medium screens - 2 columns
      @media (min-width: 700px) and (max-width: 1199px) {
        grid-template-columns: repeat(2, 1fr);
      }

      // Small screens - 1 column
      @media (max-width: 699px) {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background-color: var(--absa-gray-light);
        border-radius: 6px;

        .stat-label {
          font-weight: 500;
          color: var(--absa-gray-medium);
          font-size: 0.9rem;

          @media (max-width: 599px) {
            font-size: 0.85rem;
          }
        }

        .stat-value {
          font-weight: 700;
          color: var(--absa-dark-blue);
          font-size: 1.1rem;
          word-break: break-word;

          @media (max-width: 599px) {
            font-size: 1rem;
          }
        }

        // Mobile layout adjustments
        @media (max-width: 400px) {
          flex-direction: column;
          text-align: center;
          gap: 0.25rem;
          padding: 0.625rem;
        }
      }
    }
  }

  .alerts-card {
    mat-card-header {
      mat-card-title {
        color: var(--absa-red);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
  }
}

// Tables
.alerts-table,
.logs-table {
  width: 100%;
  background: white;

  th {
    font-weight: 700;
    color: var(--absa-dark-blue);
    background: #f8f9fa;
    padding: 1.25rem 1rem;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(0,0,0,0.1);

    @media (max-width: 599px) {
      padding: 0.75rem 0.5rem;
      font-size: 0.85rem;
    }
  }

  td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: var(--absa-gray-dark);
    font-size: 0.9rem;

    @media (max-width: 599px) {
      padding: 0.75rem 0.5rem;
      font-size: 0.85rem;
    }
  }

  tr:hover {
    background-color: rgba(206,14,45,0.02);
  }

  .clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(206,14,45,0.05) !important;
    }

    &:active {
      background-color: rgba(206,14,45,0.08) !important;
    }
  }

  // Mobile table responsiveness
  @media (max-width: 599px) {
    font-size: 0.85rem;

    // Hide less important columns on mobile
    .mat-column-id {
      display: none;
    }

    .mat-column-deadline {
      display: none;
    }
  }

  // Very small screens - show only essential columns
  @media (max-width: 400px) {
    .mat-column-type,
    .mat-column-severity {
      display: none;
    }
  }
}

// Status and Severity Chips
.status-chip,
mat-chip {
  // Base chip styling for consistent layout
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.25rem !important;
  padding: 0.375rem 0.625rem !important;
  border-radius: 12px !important;
  min-height: 28px !important;
  line-height: 1.2 !important;
  width: auto !important;
  min-width: auto !important;

  &.status-pending {
    background-color: var(--status-pending) !important;
    color: var(--absa-white) !important;
  }

  &.status-approved {
    background-color: var(--status-approved) !important;
    color: var(--absa-white) !important;
  }

  &.status-issued {
    background-color: var(--status-issued) !important;
    color: var(--absa-white) !important;
  }

  &.status-returned {
    background-color: var(--status-returned) !important;
    color: var(--absa-white) !important;
  }

  &.status-completed {
    background-color: var(--status-completed) !important;
    color: var(--absa-white) !important;
  }

  &.status-cancelled {
    background-color: var(--status-cancelled) !important;
    color: var(--absa-white) !important;
  }

  &.status-rejected {
    background-color: #e91e63 !important;
    color: var(--absa-white) !important;
  }

  &.severity-critical {
    background-color: var(--alert-critical) !important;
    color: var(--absa-white) !important;
  }

  &.severity-high {
    background-color: var(--alert-high) !important;
    color: var(--absa-white) !important;
  }

  &.severity-medium {
    background-color: var(--alert-medium) !important;
    color: var(--absa-white) !important;
  }

  &.severity-low {
    background-color: var(--alert-low) !important;
    color: var(--absa-white) !important;
  }

  &.severity-error {
    background-color: var(--alert-critical) !important;
    color: var(--absa-white) !important;
  }

  &.severity-warning {
    background-color: var(--alert-medium) !important;
    color: var(--absa-white) !important;
  }

  &.severity-info {
    background-color: var(--alert-low) !important;
    color: var(--absa-white) !important;
  }

  mat-icon {
    color: var(--absa-white) !important;
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    vertical-align: middle; // Ensure icon aligns well with text
  }

  // Ensure icon and text are on the same line and centered
  .mdc-chip__text-label, // For MDC based mat-chip
  span:not([class]) { // For plain text nodes or simple spans if mat-chip wraps text
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
  }
}

// No Data States
.no-data {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--absa-gray-medium);

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

// View All Logs
.view-all-logs {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--absa-gray-light);
}

// Action Buttons
.action-buttons {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  justify-content: flex-start;

  button {
    min-width: 32px;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    transition: all 0.2s ease;

    mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    &[color="primary"] {
      color: var(--absa-red);

      &:hover {
        background-color: rgba(206, 14, 45, 0.1);
        transform: scale(1.1);
      }
    }

    &[color="accent"] {
      color: var(--absa-gold);

      &:hover {
        background-color: rgba(255, 184, 28, 0.1);
        transform: scale(1.1);
      }
    }

    &[color="warn"] {
      color: #f44336;

      &:hover {
        background-color: rgba(244, 67, 54, 0.1);
        transform: scale(1.1);
      }
    }
  }

  // Mobile adjustments
  @media (max-width: 599px) {
    gap: 0.125rem;

    button {
      min-width: 28px;
      width: 28px;
      height: 28px;

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// No Data States
.no-data {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--absa-gray-medium);

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--absa-gray-medium);
  }

  p {
    font-size: 1.1rem;
    margin: 0;
    color: var(--absa-gray-medium);
  }
}

// View All Logs
.view-all-logs {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0,0,0,0.1);

  button {
    color: var(--absa-red);
    border-color: var(--absa-red);

    &:hover {
      background-color: rgba(206, 14, 45, 0.05);
    }
  }
}

// Enhanced Inventory Button Styles
.toolbar-inventory-btn {
  margin-right: 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: none;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;

  .btn-text {
    margin-left: 0.5rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  @media (max-width: 768px) {
    .btn-text {
      display: none;
    }
  }
}

// Enhanced Action Button Styles
.enhanced-inventory-btn {
  width: 100%;
  min-height: 80px;
  border-radius: 12px;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem;

    .action-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: var(--absa-white);
    }

    .button-text {
      flex: 1;
      text-align: left;
      margin-left: 1rem;

      .primary-text {
        display: block;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--absa-white);
      }

      .secondary-text {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        color: var(--absa-white);
        margin-top: 0.25rem;
      }
    }

    .arrow-icon {
      color: var(--absa-white);
      opacity: 0.8;
      transition: all 0.3s ease;
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);

    .arrow-icon {
      opacity: 1;
      transform: translateX(4px);
    }
  }

  @media (max-width: 768px) {
    min-height: 60px;

    .button-content {
      .action-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      .button-text {
        .primary-text {
          font-size: 1rem;
        }

        .secondary-text {
          font-size: 0.75rem;
        }
      }
    }
  }
}

// Enhanced Metric Card Styles
.metric-card {
  &.clickable {
    .metric-action {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      margin-top: 0.5rem;
      font-size: 0.875rem;
      color: var(--absa-light-blue);
      opacity: 0.8;
      transition: opacity 0.2s ease;

      .action-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }

    &:hover .metric-action {
      opacity: 1;
    }
  }
}

// Issues Table Specific Styling
.issues-table {
  .issue-title {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    strong {
      font-weight: 600;
      color: var(--absa-dark-blue);
      line-height: 1.3;
    }

    .issue-description {
      color: var(--absa-gray-medium);
      font-size: 0.8rem;
      line-height: 1.4;
      opacity: 0.8;
    }
  }

  .category-chip {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #90caf9;
  }

  // Issue Priority Styling
  .issue-priority-critical {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
    border: 1px solid #ef5350;
  }

  .issue-priority-high {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #ef6c00;
    border: 1px solid #ff9800;
  }

  .issue-priority-medium {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
    border: 1px solid #ba68c8;
  }

  .issue-priority-low {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border: 1px solid #66bb6a;
  }

  // Issue Status Styling
  .issue-status-open {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #ef6c00;
    border: 1px solid #ff9800;
  }

  .issue-status-in_progress {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    border: 1px solid #42a5f5;
  }

  .issue-status-resolved {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border: 1px solid #66bb6a;
  }

  .issue-status-closed {
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    color: #424242;
    border: 1px solid #9e9e9e;
  }

  mat-chip {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    min-height: 24px;

    mat-icon {
      font-size: 0.875rem;
      width: 0.875rem;
      height: 0.875rem;
    }
  }
}
