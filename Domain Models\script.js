// Cash Management System - Domain Models Visualizer JavaScript

// Domain Models Data Structure
const domainModels = {
  // User Management Domain
  user: [
    {
      id: 'user',
      name: 'User',
      type: 'user',
      icon: 'fas fa-user',
      description: 'Central entity representing system users with role-based access',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'ab', type: 'string', required: true },
        { name: 'fullName', type: 'string', required: true },
        { name: 'email', type: 'string', required: true },
        { name: 'contactNumber', type: 'string', required: true },
        { name: 'role', type: 'UserRole', required: true },
        { name: 'department', type: 'string', required: true }
      ],
      relationships: [
        { target: 'department', type: 'many-to-one', description: 'belongs to department' },
        { target: 'cashRequest', type: 'one-to-many', description: 'creates cash requests' },
        { target: 'notification', type: 'one-to-many', description: 'receives notifications' },
        { target: 'issue', type: 'one-to-many', description: 'reports issues' }
      ],
      businessRules: [
        { title: 'Authentication Required', description: 'User must be authenticated to access system' },
        { title: 'Role-Based Access', description: 'Access permissions based on assigned role' },
        { title: 'Unique AB Number', description: 'Employee AB number must be unique across system' }
      ],
      position: { x: 100, y: 100 }
    },
    {
      id: 'department',
      name: 'Department',
      type: 'user',
      icon: 'fas fa-building',
      description: 'Organizational units for user categorization',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'name', type: 'string', required: true }
      ],
      relationships: [
        { target: 'user', type: 'one-to-many', description: 'contains users' }
      ],
      businessRules: [
        { title: 'Unique Names', description: 'Department names must be unique' },
        { title: 'Active Departments', description: 'Only active departments can have users' }
      ],
      position: { x: 400, y: 100 }
    }
  ],

  // Cash Request Domain
  request: [
    {
      id: 'cashRequest',
      name: 'CashRequest',
      type: 'request',
      icon: 'fas fa-money-bill-wave',
      description: 'Primary entity representing a cash request throughout its lifecycle',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'requesterName', type: 'string', required: true },
        { name: 'requesterId', type: 'string', required: true },
        { name: 'department', type: 'string', required: true },
        { name: 'bankNotes', type: 'BankNote[]', required: true },
        { name: 'coins', type: 'CoinRequest[]', required: false },
        { name: 'dateRequested', type: 'Date', required: true },
        { name: 'expectedReturnDate', type: 'Date', required: false },
        { name: 'status', type: 'RequestStatus', required: true },
        { name: 'issuedBy', type: 'string', required: false },
        { name: 'comments', type: 'string', required: false }
      ],
      relationships: [
        { target: 'user', type: 'many-to-one', description: 'created by user' },
        { target: 'bankNote', type: 'one-to-many', description: 'contains bank notes' },
        { target: 'coinRequest', type: 'one-to-many', description: 'contains coin requests' },
        { target: 'notification', type: 'one-to-many', description: 'triggers notifications' },
        { target: 'issue', type: 'one-to-many', description: 'may have issues' }
      ],
      businessRules: [
        { title: 'Inventory Validation', description: 'Request must not exceed available inventory' },
        { title: 'Return Date Required', description: 'Expected return date mandatory for approved requests' },
        { title: 'Status Workflow', description: 'Must follow defined status progression' }
      ],
      position: { x: 700, y: 100 }
    },
    {
      id: 'bankNote',
      name: 'BankNote',
      type: 'request',
      icon: 'fas fa-money-bill',
      description: 'Represents requested bank note denominations',
      properties: [
        { name: 'denomination', type: 'NoteDenomination', required: true },
        { name: 'quantity', type: 'number', required: true },
        { name: 'series', type: 'NoteSeries', required: false }
      ],
      relationships: [
        { target: 'cashRequest', type: 'many-to-one', description: 'belongs to cash request' }
      ],
      businessRules: [
        { title: 'Valid Denominations', description: 'Only supported denominations allowed' },
        { title: 'Positive Quantities', description: 'Quantity must be greater than zero' }
      ],
      position: { x: 1000, y: 200 }
    },
    {
      id: 'coinRequest',
      name: 'CoinRequest',
      type: 'request',
      icon: 'fas fa-coins',
      description: 'Represents requested coin denominations with batch-based quantities',
      properties: [
        { name: 'denomination', type: 'CoinDenomination', required: true },
        { name: 'batches', type: 'number', required: true },
        { name: 'quantity', type: 'number', required: true },
        { name: 'value', type: 'number', required: true }
      ],
      relationships: [
        { target: 'cashRequest', type: 'many-to-one', description: 'belongs to cash request' }
      ],
      businessRules: [
        { title: 'Batch Calculation', description: 'Quantity must equal batches × coins per batch' },
        { title: 'Value Calculation', description: 'Value must equal quantity × denomination' }
      ],
      position: { x: 1000, y: 350 }
    }
  ],

  // Inventory Management Domain
  inventory: [
    {
      id: 'cashInventory',
      name: 'CashInventory',
      type: 'inventory',
      icon: 'fas fa-warehouse',
      description: 'Tracks available bank note inventory by series and denomination',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'noteSeries', type: 'NoteSeries', required: true },
        { name: 'denomination', type: 'NoteDenomination', required: true },
        { name: 'quantity', type: 'number', required: true },
        { name: 'value', type: 'number', required: true },
        { name: 'lastUpdated', type: 'Date', required: true },
        { name: 'updatedBy', type: 'string', required: true }
      ],
      relationships: [
        { target: 'inventoryTransaction', type: 'one-to-many', description: 'tracked by transactions' },
        { target: 'user', type: 'many-to-one', description: 'updated by user' }
      ],
      businessRules: [
        { title: 'Non-negative Quantities', description: 'Quantity cannot be negative' },
        { title: 'Value Consistency', description: 'Value must equal quantity × denomination' },
        { title: 'Low Stock Alerts', description: 'Alerts triggered at configurable thresholds' }
      ],
      position: { x: 100, y: 400 }
    },
    {
      id: 'coinInventory',
      name: 'CoinInventory',
      type: 'inventory',
      icon: 'fas fa-piggy-bank',
      description: 'Tracks available coin inventory by denomination',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'denomination', type: 'CoinDenomination', required: true },
        { name: 'quantity', type: 'number', required: true },
        { name: 'batches', type: 'number', required: true },
        { name: 'value', type: 'number', required: true },
        { name: 'lastUpdated', type: 'Date', required: true },
        { name: 'updatedBy', type: 'string', required: true }
      ],
      relationships: [
        { target: 'inventoryTransaction', type: 'one-to-many', description: 'tracked by transactions' },
        { target: 'user', type: 'many-to-one', description: 'updated by user' }
      ],
      businessRules: [
        { title: 'Batch Integrity', description: 'Batches must be complete units' },
        { title: 'Quantity Validation', description: 'Quantity must align with batch count' }
      ],
      position: { x: 400, y: 400 }
    }
  ],

  // Transaction and Audit Domain
  transaction: [
    {
      id: 'inventoryTransaction',
      name: 'InventoryTransaction',
      type: 'transaction',
      icon: 'fas fa-exchange-alt',
      description: 'Records all inventory changes for audit purposes',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'type', type: 'TransactionType', required: true },
        { name: 'inventoryId', type: 'string', required: true },
        { name: 'quantityChange', type: 'number', required: true },
        { name: 'previousQuantity', type: 'number', required: true },
        { name: 'newQuantity', type: 'number', required: true },
        { name: 'reason', type: 'string', required: true },
        { name: 'performedBy', type: 'string', required: true },
        { name: 'timestamp', type: 'Date', required: true }
      ],
      relationships: [
        { target: 'cashInventory', type: 'many-to-one', description: 'tracks cash inventory' },
        { target: 'coinInventory', type: 'many-to-one', description: 'tracks coin inventory' },
        { target: 'user', type: 'many-to-one', description: 'performed by user' }
      ],
      businessRules: [
        { title: 'Immutable Records', description: 'Transaction records cannot be modified' },
        { title: 'Quantity Consistency', description: 'Previous + change must equal new quantity' },
        { title: 'Audit Trail', description: 'All inventory changes must be recorded' }
      ],
      position: { x: 700, y: 400 }
    },
    {
      id: 'systemLog',
      name: 'SystemLog',
      type: 'transaction',
      icon: 'fas fa-clipboard-list',
      description: 'Comprehensive system activity logging',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'type', type: 'LogType', required: true },
        { name: 'category', type: 'LogCategory', required: true },
        { name: 'severity', type: 'LogSeverity', required: true },
        { name: 'title', type: 'string', required: true },
        { name: 'message', type: 'string', required: true },
        { name: 'userId', type: 'string', required: false },
        { name: 'requestId', type: 'string', required: false },
        { name: 'timestamp', type: 'Date', required: true }
      ],
      relationships: [
        { target: 'user', type: 'many-to-one', description: 'generated by user' },
        { target: 'cashRequest', type: 'many-to-one', description: 'related to request' }
      ],
      businessRules: [
        { title: 'Comprehensive Logging', description: 'All significant actions must be logged' },
        { title: 'Retention Policy', description: 'Logs retained according to compliance requirements' }
      ],
      position: { x: 1000, y: 500 }
    }
  ],

  // Notification and Communication Domain
  notification: [
    {
      id: 'notification',
      name: 'Notification',
      type: 'notification',
      icon: 'fas fa-bell',
      description: 'System notifications for users',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'type', type: 'NotificationType', required: true },
        { name: 'title', type: 'string', required: true },
        { name: 'message', type: 'string', required: true },
        { name: 'recipientId', type: 'string', required: true },
        { name: 'requestId', type: 'string', required: false },
        { name: 'isRead', type: 'boolean', required: true },
        { name: 'createdAt', type: 'Date', required: true },
        { name: 'scheduledFor', type: 'Date', required: false }
      ],
      relationships: [
        { target: 'user', type: 'many-to-one', description: 'sent to user' },
        { target: 'cashRequest', type: 'many-to-one', description: 'related to request' }
      ],
      businessRules: [
        { title: 'Automatic Generation', description: 'Notifications auto-generated for status changes' },
        { title: 'Delivery Scheduling', description: 'Notifications can be scheduled for future delivery' },
        { title: 'Read Status Tracking', description: 'System tracks notification read status' }
      ],
      position: { x: 100, y: 700 }
    }
  ],

  // Issue Management Domain
  issue: [
    {
      id: 'issue',
      name: 'Issue',
      type: 'issue',
      icon: 'fas fa-exclamation-triangle',
      description: 'Tracks problems and discrepancies in cash handling',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'title', type: 'string', required: true },
        { name: 'description', type: 'string', required: true },
        { name: 'category', type: 'IssueCategory', required: true },
        { name: 'priority', type: 'IssuePriority', required: true },
        { name: 'status', type: 'IssueStatus', required: true },
        { name: 'reportedBy', type: 'string', required: true },
        { name: 'reportedAt', type: 'Date', required: true },
        { name: 'assignedTo', type: 'string', required: false },
        { name: 'resolvedBy', type: 'string', required: false },
        { name: 'resolution', type: 'string', required: false },
        { name: 'requestId', type: 'string', required: false }
      ],
      relationships: [
        { target: 'user', type: 'many-to-one', description: 'reported by user' },
        { target: 'user', type: 'many-to-one', description: 'assigned to user' },
        { target: 'cashRequest', type: 'many-to-one', description: 'related to request' },
        { target: 'issueComment', type: 'one-to-many', description: 'has comments' }
      ],
      businessRules: [
        { title: 'Priority Assignment', description: 'Priority based on category and value impact' },
        { title: 'Resolution Tracking', description: 'All issues must have resolution documentation' },
        { title: 'Escalation Process', description: 'Critical issues escalated automatically' }
      ],
      position: { x: 400, y: 700 }
    },
    {
      id: 'issueComment',
      name: 'IssueComment',
      type: 'issue',
      icon: 'fas fa-comment',
      description: 'Comments and updates on issues',
      properties: [
        { name: 'id', type: 'string', required: true },
        { name: 'issueId', type: 'string', required: true },
        { name: 'userId', type: 'string', required: true },
        { name: 'userName', type: 'string', required: true },
        { name: 'comment', type: 'string', required: true },
        { name: 'createdAt', type: 'Date', required: true },
        { name: 'isInternal', type: 'boolean', required: false }
      ],
      relationships: [
        { target: 'issue', type: 'many-to-one', description: 'belongs to issue' },
        { target: 'user', type: 'many-to-one', description: 'written by user' }
      ],
      businessRules: [
        { title: 'Comment History', description: 'All comments preserved for audit trail' },
        { title: 'Internal Comments', description: 'Manager-only comments for sensitive information' }
      ],
      position: { x: 700, y: 800 }
    }
  ]
};

// Application State
let appState = {
  entities: [],
  relationships: [],
  selectedEntity: null,
  draggedEntity: null,
  dragOffset: { x: 0, y: 0 },
  filters: {
    showAll: true,
    domains: {
      user: true,
      request: true,
      inventory: true,
      transaction: true,
      notification: true,
      issue: true
    }
  },
  canvas: {
    width: 0,
    height: 0,
    scale: 1,
    offset: { x: 0, y: 0 }
  }
};

// DOM Elements
let elements = {};

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
  initializeElements();
  initializeData();
  setupEventListeners();
  renderEntities();
  updateStatistics();
  hideLoading();
});

function initializeElements() {
  elements = {
    entitiesContainer: document.getElementById('entitiesContainer'),
    relationshipCanvas: document.getElementById('relationshipCanvas'),
    entityModal: document.getElementById('entityModal'),
    modalTitle: document.getElementById('modalTitle'),
    entityProperties: document.getElementById('entityProperties'),
    entityRelationships: document.getElementById('entityRelationships'),
    entityRules: document.getElementById('entityRules'),
    closeModal: document.getElementById('closeModal'),
    showAllFilter: document.getElementById('showAll'),
    domainFilters: document.querySelectorAll('.domain-filter'),
    resetBtn: document.getElementById('resetBtn'),
    exportBtn: document.getElementById('exportBtn'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    toast: document.getElementById('toast'),
    toastMessage: document.getElementById('toastMessage'),
    totalEntities: document.getElementById('totalEntities'),
    totalRelationships: document.getElementById('totalRelationships'),
    visibleEntities: document.getElementById('visibleEntities')
  };
}

function initializeData() {
  // Flatten domain models into entities array
  appState.entities = [];
  appState.relationships = [];

  Object.keys(domainModels).forEach(domain => {
    domainModels[domain].forEach(entity => {
      appState.entities.push({
        ...entity,
        domain: domain,
        visible: true
      });

      // Add relationships to global relationships array
      if (entity.relationships) {
        entity.relationships.forEach(rel => {
          appState.relationships.push({
            source: entity.id,
            target: rel.target,
            type: rel.type,
            description: rel.description
          });
        });
      }
    });
  });

  // Update canvas size
  updateCanvasSize();
}

function updateCanvasSize() {
  const container = elements.entitiesContainer;
  const maxX = Math.max(...appState.entities.map(e => e.position.x + 300));
  const maxY = Math.max(...appState.entities.map(e => e.position.y + 200));

  appState.canvas.width = Math.max(maxX, window.innerWidth);
  appState.canvas.height = Math.max(maxY, window.innerHeight);

  container.style.width = appState.canvas.width + 'px';
  container.style.height = appState.canvas.height + 'px';

  elements.relationshipCanvas.setAttribute('width', appState.canvas.width);
  elements.relationshipCanvas.setAttribute('height', appState.canvas.height);
}

function setupEventListeners() {
  // Modal events
  elements.closeModal.addEventListener('click', closeModal);
  elements.entityModal.addEventListener('click', function(e) {
    if (e.target === elements.entityModal) {
      closeModal();
    }
  });

  // Filter events
  elements.showAllFilter.addEventListener('change', handleShowAllFilter);
  elements.domainFilters.forEach(filter => {
    filter.addEventListener('change', handleDomainFilter);
  });

  // Button events
  elements.resetBtn.addEventListener('click', resetLayout);
  elements.exportBtn.addEventListener('click', exportDiagram);

  // Keyboard events
  document.addEventListener('keydown', handleKeyboard);

  // Window resize
  window.addEventListener('resize', debounce(updateCanvasSize, 250));
}

function renderEntities() {
  elements.entitiesContainer.innerHTML = '';

  appState.entities.forEach(entity => {
    if (!entity.visible) return;

    const entityElement = createEntityElement(entity);
    elements.entitiesContainer.appendChild(entityElement);
  });

  // Render relationships after entities are created
  setTimeout(() => {
    renderRelationships();
  }, 100);
}

function createEntityElement(entity) {
  const entityDiv = document.createElement('div');
  entityDiv.className = `entity ${entity.type}`;
  entityDiv.id = `entity-${entity.id}`;
  entityDiv.style.left = entity.position.x + 'px';
  entityDiv.style.top = entity.position.y + 'px';
  entityDiv.setAttribute('tabindex', '0');

  // Get relationship count
  const relationshipCount = appState.relationships.filter(rel =>
    rel.source === entity.id || rel.target === entity.id
  ).length;

  entityDiv.innerHTML = `
    <div class="entity-header">
      <div class="entity-title">
        <div class="entity-icon">
          <i class="${entity.icon}"></i>
        </div>
        <div>
          <div>${entity.name}</div>
          <div class="entity-type">${entity.type} domain</div>
        </div>
      </div>
    </div>
    <div class="entity-body">
      <div class="entity-description">${entity.description}</div>
      <div class="entity-properties">
        ${entity.properties.slice(0, 3).map(prop => `
          <div class="property-item">
            <i class="fas fa-${prop.required ? 'asterisk' : 'circle'}"></i>
            <span>${prop.name}: ${prop.type}</span>
          </div>
        `).join('')}
        ${entity.properties.length > 3 ? `<div class="property-item"><i class="fas fa-ellipsis-h"></i><span>+${entity.properties.length - 3} more...</span></div>` : ''}
      </div>
    </div>
    <div class="entity-footer">
      <div class="relationship-count">
        <i class="fas fa-link"></i>
        <span>${relationshipCount} relationships</span>
      </div>
      <button class="view-details-btn" onclick="openEntityModal('${entity.id}')">
        <i class="fas fa-info-circle"></i>
      </button>
    </div>
  `;

  // Add drag functionality
  setupEntityDrag(entityDiv, entity);

  return entityDiv;
}

function setupEntityDrag(element, entity) {
  let isDragging = false;
  let startPos = { x: 0, y: 0 };
  let startEntityPos = { x: entity.position.x, y: entity.position.y };

  function handleMouseDown(e) {
    if (e.target.closest('.view-details-btn')) return;

    isDragging = true;
    element.classList.add('dragging');
    appState.draggedEntity = entity;

    startPos = { x: e.clientX, y: e.clientY };
    startEntityPos = { x: entity.position.x, y: entity.position.y };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    e.preventDefault();
  }

  function handleMouseMove(e) {
    if (!isDragging) return;

    const deltaX = e.clientX - startPos.x;
    const deltaY = e.clientY - startPos.y;

    entity.position.x = Math.max(0, startEntityPos.x + deltaX);
    entity.position.y = Math.max(0, startEntityPos.y + deltaY);

    element.style.left = entity.position.x + 'px';
    element.style.top = entity.position.y + 'px';

    // Update relationships in real-time
    renderRelationships();
  }

  function handleMouseUp() {
    if (!isDragging) return;

    isDragging = false;
    element.classList.remove('dragging');
    appState.draggedEntity = null;

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // Update canvas size if needed
    updateCanvasSize();

    showToast('Entity position updated');
  }

  element.addEventListener('mousedown', handleMouseDown);

  // Touch events for mobile
  element.addEventListener('touchstart', function(e) {
    const touch = e.touches[0];
    handleMouseDown({ clientX: touch.clientX, clientY: touch.clientY, preventDefault: () => e.preventDefault() });
  });

  element.addEventListener('touchmove', function(e) {
    const touch = e.touches[0];
    handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
    e.preventDefault();
  });

  element.addEventListener('touchend', handleMouseUp);
}

function renderRelationships() {
  const svg = elements.relationshipCanvas;
  svg.innerHTML = '';

  appState.relationships.forEach(relationship => {
    const sourceEntity = appState.entities.find(e => e.id === relationship.source);
    const targetEntity = appState.entities.find(e => e.id === relationship.target);

    if (!sourceEntity || !targetEntity || !sourceEntity.visible || !targetEntity.visible) {
      return;
    }

    const sourceElement = document.getElementById(`entity-${sourceEntity.id}`);
    const targetElement = document.getElementById(`entity-${targetEntity.id}`);

    if (!sourceElement || !targetElement) return;

    const sourceRect = sourceElement.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = elements.entitiesContainer.getBoundingClientRect();

    const sourceCenter = {
      x: sourceEntity.position.x + sourceRect.width / 2,
      y: sourceEntity.position.y + sourceRect.height / 2
    };

    const targetCenter = {
      x: targetEntity.position.x + targetRect.width / 2,
      y: targetEntity.position.y + targetRect.height / 2
    };

    // Create curved path
    const path = createRelationshipPath(sourceCenter, targetCenter);
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    line.setAttribute('d', path);
    line.setAttribute('class', 'relationship-line');
    line.setAttribute('data-source', relationship.source);
    line.setAttribute('data-target', relationship.target);

    // Add hover tooltip
    const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
    title.textContent = `${sourceEntity.name} ${relationship.description} ${targetEntity.name}`;
    line.appendChild(title);

    svg.appendChild(line);

    // Add arrow marker
    const arrow = createArrowMarker(targetCenter, sourceCenter);
    svg.appendChild(arrow);
  });
}

function createRelationshipPath(source, target) {
  const dx = target.x - source.x;
  const dy = target.y - source.y;
  const distance = Math.sqrt(dx * dx + dy * dy);

  // Create a curved path
  const controlOffset = Math.min(distance * 0.3, 100);
  const controlX = source.x + dx * 0.5 + (dy > 0 ? -controlOffset : controlOffset);
  const controlY = source.y + dy * 0.5 + (dx > 0 ? controlOffset : -controlOffset);

  return `M ${source.x} ${source.y} Q ${controlX} ${controlY} ${target.x} ${target.y}`;
}

function createArrowMarker(target, source) {
  const angle = Math.atan2(target.y - source.y, target.x - source.x);
  const arrowLength = 10;
  const arrowWidth = 6;

  const arrowTip = {
    x: target.x - Math.cos(angle) * 20,
    y: target.y - Math.sin(angle) * 20
  };

  const arrowBase1 = {
    x: arrowTip.x - Math.cos(angle - Math.PI / 6) * arrowLength,
    y: arrowTip.y - Math.sin(angle - Math.PI / 6) * arrowLength
  };

  const arrowBase2 = {
    x: arrowTip.x - Math.cos(angle + Math.PI / 6) * arrowLength,
    y: arrowTip.y - Math.sin(angle + Math.PI / 6) * arrowLength
  };

  const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
  arrow.setAttribute('points', `${arrowTip.x},${arrowTip.y} ${arrowBase1.x},${arrowBase1.y} ${arrowBase2.x},${arrowBase2.y}`);
  arrow.setAttribute('class', 'relationship-arrow');

  return arrow;
}

// Modal Functions
function openEntityModal(entityId) {
  const entity = appState.entities.find(e => e.id === entityId);
  if (!entity) return;

  appState.selectedEntity = entity;

  // Update modal content
  elements.modalTitle.textContent = entity.name;

  // Populate properties
  elements.entityProperties.innerHTML = entity.properties.map(prop => `
    <div class="property-row">
      <div class="property-name">${prop.name}</div>
      <div class="property-type">${prop.type}</div>
      <div class="property-required ${prop.required ? 'required' : 'optional'}">
        ${prop.required ? 'Required' : 'Optional'}
      </div>
    </div>
  `).join('');

  // Populate relationships
  const entityRelationships = appState.relationships.filter(rel =>
    rel.source === entityId || rel.target === entityId
  );

  elements.entityRelationships.innerHTML = entityRelationships.map(rel => {
    const isSource = rel.source === entityId;
    const relatedEntityId = isSource ? rel.target : rel.source;
    const relatedEntity = appState.entities.find(e => e.id === relatedEntityId);

    return `
      <div class="relationship-item">
        <div class="relationship-type ${rel.type.replace('-', '-')}">${rel.type}</div>
        <div class="relationship-description">
          ${isSource ? 'This entity' : relatedEntity?.name || 'Unknown'}
          ${rel.description}
          ${isSource ? relatedEntity?.name || 'Unknown' : 'this entity'}
        </div>
      </div>
    `;
  }).join('');

  // Populate business rules
  elements.entityRules.innerHTML = entity.businessRules.map(rule => `
    <div class="rule-item">
      <div class="rule-title">${rule.title}</div>
      <div class="rule-description">${rule.description}</div>
    </div>
  `).join('');

  // Show modal
  elements.entityModal.classList.add('active');

  // Highlight related entities
  highlightRelatedEntities(entityId);
}

function closeModal() {
  elements.entityModal.classList.remove('active');
  appState.selectedEntity = null;

  // Remove highlights
  clearHighlights();
}

function highlightRelatedEntities(entityId) {
  // Clear existing highlights
  clearHighlights();

  // Highlight related entities
  const relatedEntityIds = new Set();
  appState.relationships.forEach(rel => {
    if (rel.source === entityId) {
      relatedEntityIds.add(rel.target);
    } else if (rel.target === entityId) {
      relatedEntityIds.add(rel.source);
    }
  });

  relatedEntityIds.forEach(id => {
    const element = document.getElementById(`entity-${id}`);
    if (element) {
      element.style.boxShadow = '0 0 20px rgba(37, 99, 235, 0.5)';
    }
  });

  // Highlight relationship lines
  const lines = elements.relationshipCanvas.querySelectorAll('.relationship-line');
  lines.forEach(line => {
    const source = line.getAttribute('data-source');
    const target = line.getAttribute('data-target');

    if (source === entityId || target === entityId) {
      line.classList.add('highlighted');
    }
  });
}

function clearHighlights() {
  // Remove entity highlights
  const entities = document.querySelectorAll('.entity');
  entities.forEach(entity => {
    entity.style.boxShadow = '';
  });

  // Remove line highlights
  const lines = elements.relationshipCanvas.querySelectorAll('.relationship-line');
  lines.forEach(line => {
    line.classList.remove('highlighted');
  });
}

// Filter Functions
function handleShowAllFilter(e) {
  const showAll = e.target.checked;
  appState.filters.showAll = showAll;

  // Update domain filters
  elements.domainFilters.forEach(filter => {
    filter.checked = showAll;
    appState.filters.domains[filter.dataset.domain] = showAll;
  });

  updateEntityVisibility();
}

function handleDomainFilter(e) {
  const domain = e.target.dataset.domain;
  const isChecked = e.target.checked;

  appState.filters.domains[domain] = isChecked;

  // Update show all filter
  const allChecked = Object.values(appState.filters.domains).every(checked => checked);
  const noneChecked = Object.values(appState.filters.domains).every(checked => !checked);

  elements.showAllFilter.checked = allChecked;
  elements.showAllFilter.indeterminate = !allChecked && !noneChecked;
  appState.filters.showAll = allChecked;

  updateEntityVisibility();
}

function updateEntityVisibility() {
  appState.entities.forEach(entity => {
    entity.visible = appState.filters.showAll || appState.filters.domains[entity.domain];
  });

  renderEntities();
  updateStatistics();
}

// Utility Functions
function resetLayout() {
  showLoading();

  setTimeout(() => {
    // Reset entity positions to default
    Object.keys(domainModels).forEach(domain => {
      domainModels[domain].forEach((entity, index) => {
        const appEntity = appState.entities.find(e => e.id === entity.id);
        if (appEntity) {
          appEntity.position = { ...entity.position };
        }
      });
    });

    renderEntities();
    updateCanvasSize();
    hideLoading();
    showToast('Layout reset to default positions');
  }, 500);
}

function exportDiagram() {
  showLoading();

  setTimeout(() => {
    try {
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas size
      canvas.width = appState.canvas.width;
      canvas.height = appState.canvas.height;

      // Fill background
      ctx.fillStyle = '#f8fafc';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw grid pattern
      ctx.strokeStyle = '#e2e8f0';
      ctx.lineWidth = 1;
      for (let x = 0; x < canvas.width; x += 100) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }
      for (let y = 0; y < canvas.height; y += 100) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }

      // Draw entities (simplified)
      appState.entities.forEach(entity => {
        if (!entity.visible) return;

        const x = entity.position.x;
        const y = entity.position.y;
        const width = 250;
        const height = 150;

        // Draw entity box
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x, y, width, height);
        ctx.strokeStyle = getDomainColor(entity.type);
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, width, height);

        // Draw entity name
        ctx.fillStyle = '#1e293b';
        ctx.font = 'bold 16px Inter';
        ctx.fillText(entity.name, x + 10, y + 25);

        // Draw entity type
        ctx.fillStyle = '#64748b';
        ctx.font = '12px Inter';
        ctx.fillText(entity.type.toUpperCase() + ' DOMAIN', x + 10, y + 45);
      });

      // Create download link
      const link = document.createElement('a');
      link.download = 'cash-management-domain-models.png';
      link.href = canvas.toDataURL();
      link.click();

      hideLoading();
      showToast('Diagram exported successfully');
    } catch (error) {
      hideLoading();
      showToast('Export failed: ' + error.message, 'error');
    }
  }, 500);
}

function getDomainColor(domain) {
  const colors = {
    user: '#8b5cf6',
    request: '#06b6d4',
    inventory: '#10b981',
    transaction: '#f59e0b',
    notification: '#ec4899',
    issue: '#ef4444'
  };
  return colors[domain] || '#64748b';
}

function updateStatistics() {
  const totalEntities = appState.entities.length;
  const visibleEntities = appState.entities.filter(e => e.visible).length;
  const totalRelationships = appState.relationships.length;

  elements.totalEntities.textContent = totalEntities;
  elements.visibleEntities.textContent = visibleEntities;
  elements.totalRelationships.textContent = totalRelationships;
}

function showLoading() {
  elements.loadingOverlay.classList.add('active');
}

function hideLoading() {
  elements.loadingOverlay.classList.remove('active');
}

function showToast(message, type = 'success') {
  elements.toastMessage.textContent = message;
  elements.toast.className = `toast ${type}`;
  elements.toast.classList.add('active');

  setTimeout(() => {
    elements.toast.classList.remove('active');
  }, 3000);
}

function handleKeyboard(e) {
  // ESC key closes modal
  if (e.key === 'Escape' && elements.entityModal.classList.contains('active')) {
    closeModal();
  }

  // R key resets layout
  if (e.key === 'r' || e.key === 'R') {
    if (!e.ctrlKey && !e.metaKey) {
      resetLayout();
    }
  }

  // E key exports diagram
  if ((e.key === 'e' || e.key === 'E') && (e.ctrlKey || e.metaKey)) {
    e.preventDefault();
    exportDiagram();
  }
}

function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Make openEntityModal globally available
window.openEntityModal = openEntityModal;
